#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法参数有效性检查工具
验证参数传递链路：配置文件 → 算法调用 → rectpack库执行
"""

import logging
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass
import traceback

log = logging.getLogger(__name__)


@dataclass
class ParameterCheckResult:
    """参数检查结果"""
    parameter_name: str
    config_value: Any
    ui_value: Any
    algorithm_value: Any
    is_consistent: bool
    error_message: str = ""


class RectPackParameterChecker:
    """RectPack算法参数有效性检查器"""
    
    def __init__(self, config_manager=None):
        """
        初始化参数检查器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.check_results = []
        
    def check_parameter_chain(self, ui_settings: Dict[str, Any] = None) -> List[ParameterCheckResult]:
        """
        检查参数传递链路的完整性
        
        Args:
            ui_settings: UI设置值（可选）
            
        Returns:
            List[ParameterCheckResult]: 检查结果列表
        """
        self.check_results = []
        
        try:
            # 1. 从配置管理器获取参数
            config_values = self._get_config_parameters()
            log.info(f"从配置获取到 {len(config_values)} 个参数")
            
            # 2. 从UI获取参数（如果提供）
            if ui_settings:
                log.info(f"从UI获取到 {len(ui_settings)} 个参数")
            
            # 3. 检查核心参数的一致性
            core_params = [
                'rectpack_rotation_enabled',
                'rectpack_sort_strategy', 
                'rectpack_pack_algorithm',
                'rectpack_optimization_iterations',
                'rectpack_min_utilization_threshold',
                'rectpack_rotation_penalty',
                'rectpack_aspect_ratio_preference'
            ]
            
            for param in core_params:
                result = self._check_single_parameter(
                    param, 
                    config_values.get(param),
                    ui_settings.get(param) if ui_settings else None
                )
                self.check_results.append(result)
            
            # 4. 生成检查报告
            self._generate_check_report()
            
        except Exception as e:
            log.error(f"参数链路检查失败: {str(e)}")
            log.error(traceback.format_exc())
            
        return self.check_results
    
    def _get_config_parameters(self) -> Dict[str, Any]:
        """从配置管理器获取RectPack参数"""
        if not self.config_manager:
            log.warning("配置管理器未提供")
            return {}
            
        try:
            return self.config_manager.get_rectpack_settings()
        except Exception as e:
            log.error(f"获取配置参数失败: {str(e)}")
            return {}
    
    def _check_single_parameter(self, param_name: str, config_value: Any, ui_value: Any = None) -> ParameterCheckResult:
        """
        检查单个参数的一致性
        
        Args:
            param_name: 参数名称
            config_value: 配置值
            ui_value: UI值
            
        Returns:
            ParameterCheckResult: 检查结果
        """
        try:
            # 检查参数是否在预期范围内
            is_valid, error_msg = self._validate_parameter_value(param_name, config_value)
            
            # 检查UI和配置的一致性
            is_consistent = True
            if ui_value is not None and config_value != ui_value:
                is_consistent = False
                error_msg += f" UI值({ui_value})与配置值({config_value})不一致"
            
            return ParameterCheckResult(
                parameter_name=param_name,
                config_value=config_value,
                ui_value=ui_value,
                algorithm_value=None,  # 需要从算法实例获取
                is_consistent=is_consistent and is_valid,
                error_message=error_msg
            )
            
        except Exception as e:
            return ParameterCheckResult(
                parameter_name=param_name,
                config_value=config_value,
                ui_value=ui_value,
                algorithm_value=None,
                is_consistent=False,
                error_message=f"检查失败: {str(e)}"
            )
    
    def _validate_parameter_value(self, param_name: str, value: Any) -> Tuple[bool, str]:
        """
        验证参数值的有效性
        
        Args:
            param_name: 参数名称
            value: 参数值
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if value is None:
            return False, f"{param_name} 值为空"
        
        # 根据参数类型进行验证
        if param_name == 'rectpack_rotation_enabled':
            if not isinstance(value, bool):
                return False, f"旋转启用参数应为布尔值，当前为: {type(value)}"
                
        elif param_name == 'rectpack_sort_strategy':
            if not isinstance(value, int) or not (0 <= value <= 5):
                return False, f"排序策略应为0-5的整数，当前为: {value}"
                
        elif param_name == 'rectpack_pack_algorithm':
            if not isinstance(value, int) or not (0 <= value <= 2):
                return False, f"装箱算法应为0-2的整数，当前为: {value}"
                
        elif param_name == 'rectpack_optimization_iterations':
            if not isinstance(value, int) or not (1 <= value <= 100):
                return False, f"优化迭代次数应为1-100的整数，当前为: {value}"
                
        elif param_name == 'rectpack_min_utilization_threshold':
            if not isinstance(value, (int, float)) or not (50.0 <= value <= 100.0):
                return False, f"最小利用率阈值应为50.0-100.0的数值，当前为: {value}"
                
        elif param_name == 'rectpack_rotation_penalty':
            if not isinstance(value, (int, float)) or not (0.0 <= value <= 1.0):
                return False, f"旋转惩罚应为0.0-1.0的数值，当前为: {value}"
                
        elif param_name == 'rectpack_aspect_ratio_preference':
            if not isinstance(value, (int, float)) or not (0.1 <= value <= 10.0):
                return False, f"宽高比偏好应为0.1-10.0的数值，当前为: {value}"
        
        return True, ""
    
    def _generate_check_report(self):
        """生成检查报告"""
        total_params = len(self.check_results)
        valid_params = sum(1 for result in self.check_results if result.is_consistent)
        
        log.info("=" * 60)
        log.info("RectPack参数有效性检查报告")
        log.info("=" * 60)
        log.info(f"总参数数量: {total_params}")
        log.info(f"有效参数数量: {valid_params}")
        log.info(f"无效参数数量: {total_params - valid_params}")
        log.info(f"参数有效率: {(valid_params/total_params*100):.1f}%" if total_params > 0 else "0%")
        
        # 详细报告
        for result in self.check_results:
            status = "✅" if result.is_consistent else "❌"
            log.info(f"{status} {result.parameter_name}: 配置值={result.config_value}")
            if result.ui_value is not None:
                log.info(f"    UI值={result.ui_value}")
            if result.error_message:
                log.warning(f"    错误: {result.error_message}")
        
        log.info("=" * 60)
    
    def check_algorithm_execution(self, arranger_instance) -> Dict[str, Any]:
        """
        检查算法执行时的参数使用情况
        
        Args:
            arranger_instance: RectPack排列器实例
            
        Returns:
            Dict[str, Any]: 算法实际使用的参数
        """
        algorithm_params = {}
        
        try:
            if hasattr(arranger_instance, 'rotation_enabled'):
                algorithm_params['rotation_enabled'] = arranger_instance.rotation_enabled
                
            if hasattr(arranger_instance, 'sort_key'):
                algorithm_params['sort_key'] = arranger_instance.sort_key
                
            if hasattr(arranger_instance, 'pack_algo'):
                algorithm_params['pack_algo'] = arranger_instance.pack_algo
                
            if hasattr(arranger_instance, 'optimization_iterations'):
                algorithm_params['optimization_iterations'] = arranger_instance.optimization_iterations
                
            if hasattr(arranger_instance, 'min_utilization_threshold'):
                algorithm_params['min_utilization_threshold'] = arranger_instance.min_utilization_threshold
                
            if hasattr(arranger_instance, 'rotation_penalty'):
                algorithm_params['rotation_penalty'] = arranger_instance.rotation_penalty
                
            if hasattr(arranger_instance, 'aspect_ratio_preference'):
                algorithm_params['aspect_ratio_preference'] = arranger_instance.aspect_ratio_preference
            
            log.info(f"从算法实例获取到 {len(algorithm_params)} 个参数")
            
            # 记录算法实际使用的参数
            for param, value in algorithm_params.items():
                log.info(f"算法参数 {param}: {value}")
                
        except Exception as e:
            log.error(f"检查算法执行参数失败: {str(e)}")
            
        return algorithm_params
    
    def verify_production_impact(self, test_results: Dict[str, Any], production_results: Dict[str, Any]) -> bool:
        """
        验证参数变更对生产模式的实际影响
        
        Args:
            test_results: 测试模式结果
            production_results: 生产模式结果
            
        Returns:
            bool: 参数是否真正影响了生产结果
        """
        try:
            # 比较关键指标
            test_utilization = test_results.get('utilization_rate', 0)
            prod_utilization = production_results.get('utilization_rate', 0)
            
            test_image_count = test_results.get('total_images', 0)
            prod_image_count = production_results.get('total_images', 0)
            
            test_canvas_count = test_results.get('canvas_count', 0)
            prod_canvas_count = production_results.get('canvas_count', 0)
            
            # 检查结果一致性
            utilization_diff = abs(test_utilization - prod_utilization)
            image_count_match = test_image_count == prod_image_count
            canvas_count_match = test_canvas_count == prod_canvas_count
            
            log.info("生产模式影响验证:")
            log.info(f"利用率差异: {utilization_diff:.2f}% (测试: {test_utilization:.2f}%, 生产: {prod_utilization:.2f}%)")
            log.info(f"图片数量匹配: {image_count_match} (测试: {test_image_count}, 生产: {prod_image_count})")
            log.info(f"画布数量匹配: {canvas_count_match} (测试: {test_canvas_count}, 生产: {prod_canvas_count})")
            
            # 判断参数是否有效影响
            is_effective = (
                utilization_diff < 5.0 and  # 利用率差异小于5%
                image_count_match and       # 图片数量一致
                canvas_count_match          # 画布数量一致
            )
            
            if is_effective:
                log.info("✅ 参数变更有效影响了生产模式结果")
            else:
                log.warning("❌ 参数变更未能有效影响生产模式结果")
                
            return is_effective
            
        except Exception as e:
            log.error(f"验证生产模式影响失败: {str(e)}")
            return False


def create_parameter_checker(config_manager=None) -> RectPackParameterChecker:
    """
    创建参数检查器实例
    
    Args:
        config_manager: 配置管理器
        
    Returns:
        RectPackParameterChecker: 参数检查器实例
    """
    return RectPackParameterChecker(config_manager)


def quick_check_parameters(config_manager=None, ui_settings: Dict[str, Any] = None) -> bool:
    """
    快速检查参数有效性
    
    Args:
        config_manager: 配置管理器
        ui_settings: UI设置
        
    Returns:
        bool: 参数是否全部有效
    """
    checker = create_parameter_checker(config_manager)
    results = checker.check_parameter_chain(ui_settings)
    
    # 返回是否所有参数都有效
    return all(result.is_consistent for result in results)


if __name__ == "__main__":
    # 测试用例
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        
        # 创建配置管理器
        config_manager = ConfigManagerDuckDB()
        
        # 创建参数检查器
        checker = create_parameter_checker(config_manager)
        
        # 执行参数检查
        results = checker.check_parameter_chain()
        
        # 输出结果
        valid_count = sum(1 for r in results if r.is_consistent)
        total_count = len(results)
        
        print(f"\n参数检查完成: {valid_count}/{total_count} 个参数有效")
        
        if valid_count == total_count:
            print("✅ 所有参数传递链路正常")
        else:
            print("❌ 存在参数传递问题，请检查日志")
            
    except Exception as e:
        print(f"参数检查失败: {str(e)}")
