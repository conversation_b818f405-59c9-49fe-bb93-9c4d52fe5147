#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法统计报告生成器
生成详细的排列统计和画布利用率数据
"""

import logging
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

log = logging.getLogger(__name__)


@dataclass
class CanvasStatistics:
    """画布统计信息"""
    canvas_id: int
    width_cm: float
    height_cm: float
    actual_height_cm: float
    total_images: int
    a_class_images: int
    b_class_images: int
    c_class_images: int
    total_area_cm2: float
    used_area_cm2: float
    utilization_rate: float
    processing_time_seconds: float


@dataclass
class AlgorithmStatistics:
    """算法统计信息"""
    algorithm_name: str = "RectPack"
    total_images: int = 0
    total_canvases: int = 0
    total_processing_time: float = 0.0
    average_utilization: float = 0.0
    best_utilization: float = 0.0
    worst_utilization: float = 0.0
    parameters_used: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.parameters_used is None:
            self.parameters_used = {}


class RectPackStatisticsGenerator:
    """RectPack统计报告生成器"""
    
    def __init__(self):
        """初始化统计生成器"""
        self.canvas_stats: List[CanvasStatistics] = []
        self.algorithm_stats = AlgorithmStatistics()
        self.start_time = time.time()
        self.classification_stats = {
            'A类': {'count': 0, 'total_area': 0.0},
            'B类': {'count': 0, 'total_area': 0.0},
            'C类': {'count': 0, 'total_area': 0.0}
        }
    
    def add_canvas_statistics(self, canvas_id: int, width_cm: float, height_cm: float,
                            actual_height_cm: float, images_data: List[Dict[str, Any]],
                            processing_time: float = 0.0) -> None:
        """
        添加画布统计信息
        
        Args:
            canvas_id: 画布ID
            width_cm: 画布宽度(cm)
            height_cm: 画布最大高度(cm)
            actual_height_cm: 实际使用高度(cm)
            images_data: 图片数据列表
            processing_time: 处理时间(秒)
        """
        # 统计图片分类
        a_count = b_count = c_count = 0
        total_used_area = 0.0
        
        for img in images_data:
            img_width = img.get('width', 0)
            img_height = img.get('height', 0)
            img_area = (img_width * img_height) / 10000  # 转换为cm²
            total_used_area += img_area
            
            # 根据图片分类统计
            img_class = img.get('class', 'C')
            if img_class == 'A':
                a_count += 1
                self.classification_stats['A类']['count'] += 1
                self.classification_stats['A类']['total_area'] += img_area
            elif img_class == 'B':
                b_count += 1
                self.classification_stats['B类']['count'] += 1
                self.classification_stats['B类']['total_area'] += img_area
            else:
                c_count += 1
                self.classification_stats['C类']['count'] += 1
                self.classification_stats['C类']['total_area'] += img_area
        
        # 计算利用率
        total_area = width_cm * actual_height_cm
        utilization = (total_used_area / total_area * 100) if total_area > 0 else 0.0
        
        # 创建画布统计
        canvas_stat = CanvasStatistics(
            canvas_id=canvas_id,
            width_cm=width_cm,
            height_cm=height_cm,
            actual_height_cm=actual_height_cm,
            total_images=len(images_data),
            a_class_images=a_count,
            b_class_images=b_count,
            c_class_images=c_count,
            total_area_cm2=total_area,
            used_area_cm2=total_used_area,
            utilization_rate=utilization,
            processing_time_seconds=processing_time
        )
        
        self.canvas_stats.append(canvas_stat)
        log.info(f"添加画布统计: 画布{canvas_id}, 利用率{utilization:.1f}%, 图片{len(images_data)}张")
    
    def finalize_statistics(self, algorithm_params: Dict[str, Any] = None) -> None:
        """
        完成统计计算
        
        Args:
            algorithm_params: 算法参数
        """
        if not self.canvas_stats:
            log.warning("没有画布统计数据")
            return
        
        # 计算总体统计
        total_images = sum(canvas.total_images for canvas in self.canvas_stats)
        total_time = time.time() - self.start_time
        utilizations = [canvas.utilization_rate for canvas in self.canvas_stats]
        
        self.algorithm_stats = AlgorithmStatistics(
            algorithm_name="RectPack",
            total_images=total_images,
            total_canvases=len(self.canvas_stats),
            total_processing_time=total_time,
            average_utilization=sum(utilizations) / len(utilizations) if utilizations else 0.0,
            best_utilization=max(utilizations) if utilizations else 0.0,
            worst_utilization=min(utilizations) if utilizations else 0.0,
            parameters_used=algorithm_params or {}
        )
        
        log.info(f"统计完成: {total_images}张图片, {len(self.canvas_stats)}个画布, 平均利用率{self.algorithm_stats.average_utilization:.1f}%")
    
    def generate_markdown_report(self) -> str:
        """生成Markdown格式的统计报告"""
        if not self.canvas_stats:
            return "# RectPack算法统计报告\n\n**错误**: 没有统计数据"
        
        report_lines = [
            "# RectPack算法统计报告",
            "",
            f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**算法版本**: {self.algorithm_stats.algorithm_name}",
            "",
            "## 总体统计",
            "",
            f"- **总图片数量**: {self.algorithm_stats.total_images:,} 张",
            f"- **总画布数量**: {self.algorithm_stats.total_canvases} 个",
            f"- **总处理时间**: {self.algorithm_stats.total_processing_time:.2f} 秒",
            f"- **平均利用率**: {self.algorithm_stats.average_utilization:.2f}%",
            f"- **最高利用率**: {self.algorithm_stats.best_utilization:.2f}%",
            f"- **最低利用率**: {self.algorithm_stats.worst_utilization:.2f}%",
            ""
        ]
        
        # 图片分类统计
        report_lines.extend([
            "## 图片分类统计",
            "",
            "| 分类 | 数量 | 占比 | 总面积(cm²) | 平均面积(cm²) |",
            "|------|------|------|-------------|---------------|"
        ])
        
        total_images = self.algorithm_stats.total_images
        for class_name, stats in self.classification_stats.items():
            count = stats['count']
            area = stats['total_area']
            percentage = (count / total_images * 100) if total_images > 0 else 0
            avg_area = (area / count) if count > 0 else 0
            
            report_lines.append(
                f"| {class_name} | {count:,} | {percentage:.1f}% | {area:,.1f} | {avg_area:.1f} |"
            )
        
        # 画布详细统计
        report_lines.extend([
            "",
            "## 画布详细统计",
            "",
            "| 画布ID | 尺寸(cm) | 实际高度(cm) | 图片数 | A类 | B类 | C类 | 利用率 | 处理时间(s) |",
            "|--------|----------|--------------|--------|-----|-----|-----|--------|-------------|"
        ])
        
        for canvas in self.canvas_stats:
            report_lines.append(
                f"| {canvas.canvas_id} | {canvas.width_cm:.0f}×{canvas.height_cm:.0f} | "
                f"{canvas.actual_height_cm:.1f} | {canvas.total_images} | "
                f"{canvas.a_class_images} | {canvas.b_class_images} | {canvas.c_class_images} | "
                f"{canvas.utilization_rate:.1f}% | {canvas.processing_time_seconds:.2f} |"
            )
        
        # 算法参数
        if self.algorithm_stats.parameters_used:
            report_lines.extend([
                "",
                "## 算法参数配置",
                "",
                "| 参数名 | 参数值 | 说明 |",
                "|--------|--------|------|"
            ])
            
            param_descriptions = {
                'rotation_enabled': '启用旋转',
                'sort_strategy': '排序策略',
                'pack_algorithm': '装箱算法',
                'optimization_iterations': '优化迭代次数',
                'min_utilization_threshold': '最小利用率阈值',
                'rotation_penalty': '旋转惩罚',
                'aspect_ratio_preference': '宽高比偏好',
                'max_processing_time': '最大处理时间',
                'batch_size': '批处理大小',
                'memory_limit_mb': '内存限制'
            }
            
            for param_name, param_value in self.algorithm_stats.parameters_used.items():
                description = param_descriptions.get(param_name, '未知参数')
                report_lines.append(f"| {param_name} | {param_value} | {description} |")
        
        # 性能分析
        report_lines.extend([
            "",
            "## 性能分析",
            "",
            f"- **平均每张图片处理时间**: {(self.algorithm_stats.total_processing_time / self.algorithm_stats.total_images):.3f} 秒",
            f"- **平均每个画布处理时间**: {(self.algorithm_stats.total_processing_time / self.algorithm_stats.total_canvases):.2f} 秒",
            ""
        ])
        
        # 利用率分析
        if len(self.canvas_stats) > 1:
            utilizations = [canvas.utilization_rate for canvas in self.canvas_stats]
            std_dev = (sum((x - self.algorithm_stats.average_utilization) ** 2 for x in utilizations) / len(utilizations)) ** 0.5
            
            report_lines.extend([
                "## 利用率分析",
                "",
                f"- **利用率标准差**: {std_dev:.2f}%",
                f"- **利用率变异系数**: {(std_dev / self.algorithm_stats.average_utilization * 100):.1f}%",
                ""
            ])
        
        # 建议和总结
        report_lines.extend([
            "## 总结与建议",
            "",
            self._generate_recommendations(),
            ""
        ])
        
        return "\n".join(report_lines)
    
    def _generate_recommendations(self) -> str:
        """生成建议和总结"""
        recommendations = []
        
        avg_util = self.algorithm_stats.average_utilization
        
        if avg_util >= 85:
            recommendations.append("✅ **优秀**: 平均利用率超过85%，算法表现优秀")
        elif avg_util >= 80:
            recommendations.append("✅ **良好**: 平均利用率达到80%以上，符合预期目标")
        elif avg_util >= 75:
            recommendations.append("⚠️ **一般**: 平均利用率在75-80%之间，建议优化参数")
        else:
            recommendations.append("❌ **需要改进**: 平均利用率低于75%，需要检查算法配置")
        
        # 根据画布数量给出建议
        if self.algorithm_stats.total_canvases > 5:
            recommendations.append("📊 **多画布场景**: 建议考虑增加优化迭代次数以提高整体利用率")
        
        # 根据处理时间给出建议
        avg_time_per_image = self.algorithm_stats.total_processing_time / self.algorithm_stats.total_images
        if avg_time_per_image > 1.0:
            recommendations.append("⏱️ **性能建议**: 平均每张图片处理时间较长，建议优化性能参数")
        
        return "\n".join(f"- {rec}" for rec in recommendations)
    
    def save_report(self, output_path: str) -> bool:
        """
        保存统计报告到文件
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            bool: 是否成功保存
        """
        try:
            report_content = self.generate_markdown_report()
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            log.info(f"统计报告已保存到: {output_path}")
            return True
            
        except Exception as e:
            log.error(f"保存统计报告失败: {str(e)}")
            return False
    
    def export_json_data(self, output_path: str) -> bool:
        """
        导出JSON格式的统计数据
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            bool: 是否成功导出
        """
        try:
            data = {
                'algorithm_stats': {
                    'algorithm_name': self.algorithm_stats.algorithm_name,
                    'total_images': self.algorithm_stats.total_images,
                    'total_canvases': self.algorithm_stats.total_canvases,
                    'total_processing_time': self.algorithm_stats.total_processing_time,
                    'average_utilization': self.algorithm_stats.average_utilization,
                    'best_utilization': self.algorithm_stats.best_utilization,
                    'worst_utilization': self.algorithm_stats.worst_utilization,
                    'parameters_used': self.algorithm_stats.parameters_used
                },
                'classification_stats': self.classification_stats,
                'canvas_stats': [
                    {
                        'canvas_id': canvas.canvas_id,
                        'width_cm': canvas.width_cm,
                        'height_cm': canvas.height_cm,
                        'actual_height_cm': canvas.actual_height_cm,
                        'total_images': canvas.total_images,
                        'a_class_images': canvas.a_class_images,
                        'b_class_images': canvas.b_class_images,
                        'c_class_images': canvas.c_class_images,
                        'total_area_cm2': canvas.total_area_cm2,
                        'used_area_cm2': canvas.used_area_cm2,
                        'utilization_rate': canvas.utilization_rate,
                        'processing_time_seconds': canvas.processing_time_seconds
                    }
                    for canvas in self.canvas_stats
                ],
                'generation_time': datetime.now().isoformat()
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            log.info(f"JSON统计数据已导出到: {output_path}")
            return True
            
        except Exception as e:
            log.error(f"导出JSON数据失败: {str(e)}")
            return False


def create_statistics_generator() -> RectPackStatisticsGenerator:
    """创建统计生成器实例"""
    return RectPackStatisticsGenerator()


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建测试统计生成器
    generator = RectPackStatisticsGenerator()
    
    # 添加测试数据
    test_images = [
        {'width': 1000, 'height': 500, 'class': 'A'},
        {'width': 800, 'height': 600, 'class': 'B'},
        {'width': 400, 'height': 300, 'class': 'C'},
        {'width': 300, 'height': 200, 'class': 'C'}
    ]
    
    generator.add_canvas_statistics(1, 202, 5000, 1200, test_images, 2.5)
    
    # 完成统计
    test_params = {
        'rotation_enabled': True,
        'optimization_iterations': 5,
        'min_utilization_threshold': 80.0
    }
    generator.finalize_statistics(test_params)
    
    # 生成报告
    report = generator.generate_markdown_report()
    print(report)
