#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from core.intelligent_grouping_optimizer import IntelligentGroupingOptimizer
    print("✅ 导入成功")
    
    # 创建优化器
    optimizer = IntelligentGroupingOptimizer(
        canvas_width_cm=163.0,
        target_utilization=0.93,
        production_mode=True,
        log_signal=None
    )
    print("✅ 优化器创建成功")
    
    # 测试多种尺寸的图片
    test_images = [
        # 137x51cm - 十位数5，应该旋转（3张/行，93.9%利用率）
        {'pattern_name': 'test_137x51_1', 'width_cm': 137.0, 'height_cm': 51.0},
        {'pattern_name': 'test_137x51_2', 'width_cm': 137.0, 'height_cm': 51.0},
        {'pattern_name': 'test_137x51_3', 'width_cm': 137.0, 'height_cm': 51.0},

        # 67x52cm - 十位数5，应该旋转（3张/行，95.7%利用率）
        {'pattern_name': 'test_67x52_1', 'width_cm': 67.0, 'height_cm': 52.0},
        {'pattern_name': 'test_67x52_2', 'width_cm': 67.0, 'height_cm': 52.0},
        {'pattern_name': 'test_67x52_3', 'width_cm': 67.0, 'height_cm': 52.0},

        # 90x60cm - 十位数6，应该旋转（2张/行，73.6%利用率）
        {'pattern_name': 'test_90x60_1', 'width_cm': 90.0, 'height_cm': 60.0},
        {'pattern_name': 'test_90x60_2', 'width_cm': 90.0, 'height_cm': 60.0},

        # 80x70cm - 十位数7，应该旋转（2张/行，85.9%利用率）
        {'pattern_name': 'test_80x70_1', 'width_cm': 80.0, 'height_cm': 70.0},
        {'pattern_name': 'test_80x70_2', 'width_cm': 80.0, 'height_cm': 70.0},
        {'pattern_name': 'test_80x70_3', 'width_cm': 80.0, 'height_cm': 70.0},

        # 100x25cm - 十位数2，可能不旋转（利用率不足）
        {'pattern_name': 'test_100x25_1', 'width_cm': 100.0, 'height_cm': 25.0},
        {'pattern_name': 'test_100x25_2', 'width_cm': 100.0, 'height_cm': 25.0},
    ]
    print(f"✅ 测试图片准备完成: {len(test_images)}张")
    
    # 执行优化
    print("🔍 开始优化...")
    result = optimizer.optimize_image_grouping(test_images)
    print("✅ 优化完成")
    
    # 检查结果
    rotation_count = sum(1 for img in result if img.get('intelligent_rotation_suggested', False))
    print(f"📊 总体结果: {rotation_count}/{len(result)}张图片建议旋转")
    print()

    # 按尺寸分组统计
    size_groups = {}
    for img in result:
        width_cm = img.get('width_cm', 0)
        height_cm = img.get('height_cm', 0)
        size_key = f"{width_cm}x{height_cm}"

        if size_key not in size_groups:
            size_groups[size_key] = {'total': 0, 'rotated': 0, 'examples': []}

        size_groups[size_key]['total'] += 1
        if img.get('intelligent_rotation_suggested', False):
            size_groups[size_key]['rotated'] += 1

        if len(size_groups[size_key]['examples']) < 2:
            rotation = img.get('intelligent_rotation_suggested', False)
            reason = img.get('optimization_reason', '无')
            size_groups[size_key]['examples'].append(f"{'旋转' if rotation else '不旋转'} - {reason}")

    print("📊 各尺寸优化结果:")
    for size_key, stats in size_groups.items():
        rotation_rate = stats['rotated'] / stats['total'] if stats['total'] > 0 else 0
        print(f"  {size_key}cm: {stats['rotated']}/{stats['total']} ({rotation_rate:.1%})")
        for example in stats['examples']:
            print(f"    - {example}")

    print()

    # 手动验证十位数计算
    print("🧮 十位数计算验证:")
    target_tens = int((163 - 10) / 10)
    print(f"  画布163cm → 目标十位数: {target_tens}")

    test_cases = [
        (51, "137x51cm"),
        (52, "67x52cm"),
        (60, "90x60cm"),
        (70, "80x70cm"),
        (25, "100x25cm"),
    ]

    for edge, desc in test_cases:
        tens_digit = int(edge // 10)
        fit_count = target_tens // tens_digit if tens_digit > 0 else 0
        utilization = (fit_count * edge) / 163 if fit_count > 0 else 0
        print(f"  {desc} → 十位数{tens_digit}, {fit_count}张/行, {utilization:.1%}利用率")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
