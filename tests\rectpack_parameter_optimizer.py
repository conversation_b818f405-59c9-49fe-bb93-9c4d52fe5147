#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法参数优化器
使用提供的测试数据优化rectpack算法参数，找到最佳配置
"""

import logging
import time
from typing import List, Tuple, Dict, Any
from dataclasses import dataclass

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

@dataclass
class OptimizationResult:
    """优化结果数据类"""
    params: Dict[str, Any]
    utilization: float
    containers_used: int
    total_area: float
    container_area: float
    processing_time: float

class RectPackParameterOptimizer:
    """RectPack参数优化器"""

    def __init__(self):
        self.test_data = []
        self.canvas_width = 202  # 实际画布宽度 202cm
        self.canvas_height = 5000  # 最大高度 5000cm
        self.best_result = None

    def parse_test_data(self, data_text: str) -> List[Tuple[int, int]]:
        """
        解析测试数据

        Args:
            data_text: 测试数据文本

        Returns:
            List[Tuple[int, int]]: 图片尺寸列表 (宽, 高)
        """
        rectangles = []
        lines = data_text.strip().split('\n')

        for line in lines[1:]:  # 跳过标题行
            if line.strip():
                parts = line.strip().split('\t')
                if len(parts) >= 3:
                    try:
                        width = int(float(parts[0]))
                        height = int(float(parts[1]))
                        count = int(float(parts[2]))

                        # 添加指定数量的矩形
                        for _ in range(count):
                            rectangles.append((width, height))
                    except (ValueError, IndexError):
                        continue

        log.info(f"解析测试数据完成，共 {len(rectangles)} 个矩形")
        return rectangles

    def test_rectpack_configuration(self,
                                  sort_strategy: int,
                                  pack_algorithm: int,
                                  rotation_enabled: bool,
                                  rectangles: List[Tuple[int, int]]) -> OptimizationResult:
        """
        测试特定的rectpack配置，支持多容器优化

        Args:
            sort_strategy: 排序策略
            pack_algorithm: 装箱算法
            rotation_enabled: 是否启用旋转
            rectangles: 矩形列表

        Returns:
            OptimizationResult: 优化结果
        """
        try:
            from rectpack import newPacker, SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO

            start_time = time.time()

            # 排序策略映射
            sort_strategies = [SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO]

            # 多容器装箱策略
            containers_used = 0
            total_area = sum(w * h for w, h in rectangles)
            total_container_area = 0
            remaining_rectangles = rectangles.copy()

            while remaining_rectangles:
                containers_used += 1

                # 创建新的装箱器 - 使用简化的API
                packer = newPacker(
                    sort_algo=sort_strategies[sort_strategy],
                    rotation=rotation_enabled
                )

                # 添加容器
                packer.add_bin(self.canvas_width, self.canvas_height)

                # 添加剩余矩形，确保ID是整数
                for i, (width, height) in enumerate(remaining_rectangles):
                    rect_id = int(containers_used * 10000 + i)  # 确保ID是整数
                    packer.add_rect(int(width), int(height), rid=rect_id)

                # 执行装箱
                packer.pack()

                # 检查哪些矩形被放置
                placed_indices = set()
                max_y = 0

                for bin_id in packer:
                    for rect in packer[bin_id]:
                        # 将全局ID转换回本地索引
                        local_index = int(rect.rid - containers_used * 10000)
                        placed_indices.add(local_index)
                        max_y = max(max_y, rect.y + rect.height)

                # 如果没有矩形被放置，停止
                if not placed_indices:
                    break

                # 计算当前容器的实际面积
                current_container_area = self.canvas_width * max_y if max_y > 0 else 0
                total_container_area += current_container_area

                # 移除已放置的矩形
                remaining_rectangles = [rect for i, rect in enumerate(remaining_rectangles)
                                      if i not in placed_indices]

                # 防止无限循环
                if containers_used > 20:  # 最多20个容器
                    break

            # 计算整体利用率
            utilization = (total_area / total_container_area) * 100 if total_container_area > 0 else 0

            processing_time = time.time() - start_time

            params = {
                'sort_strategy': sort_strategy,
                'pack_algorithm': pack_algorithm,
                'rotation_enabled': rotation_enabled
            }

            return OptimizationResult(
                params=params,
                utilization=utilization,
                containers_used=containers_used,
                total_area=total_area,
                container_area=total_container_area,
                processing_time=processing_time
            )

        except ImportError:
            log.error("RectPack库不可用，使用简化测试")
            return self._test_without_rectpack(sort_strategy, pack_algorithm, rotation_enabled, rectangles)
        except Exception as e:
            log.error(f"测试配置失败: {str(e)}")
            return None

    def _test_without_rectpack(self, sort_strategy: int, pack_algorithm: int,
                              rotation_enabled: bool, rectangles: List[Tuple[int, int]]) -> OptimizationResult:
        """
        简化测试函数，不依赖rectpack库
        """
        start_time = time.time()

        # 简单的面积计算
        total_area = sum(w * h for w, h in rectangles)

        # 估算容器数量（简化算法）
        single_container_area = self.canvas_width * self.canvas_height
        estimated_containers = max(1, int(total_area / (single_container_area * 0.8)))  # 假设80%利用率

        # 估算实际使用面积
        estimated_used_area = estimated_containers * single_container_area * 0.75  # 假设75%利用率
        utilization = (total_area / estimated_used_area) * 100 if estimated_used_area > 0 else 0

        processing_time = time.time() - start_time

        params = {
            'sort_strategy': sort_strategy,
            'pack_algorithm': pack_algorithm,
            'rotation_enabled': rotation_enabled
        }

        return OptimizationResult(
            params=params,
            utilization=utilization,
            containers_used=estimated_containers,
            total_area=total_area,
            container_area=estimated_used_area,
            processing_time=processing_time
        )

    def optimize_parameters(self, rectangles: List[Tuple[int, int]]) -> OptimizationResult:
        """
        优化参数，找到最佳配置，重点测试旋转功能

        Args:
            rectangles: 矩形列表

        Returns:
            OptimizationResult: 最佳优化结果
        """
        log.info("开始参数优化...")
        log.info(f"测试数据统计: 共 {len(rectangles)} 个矩形")

        # 分析测试数据中的旋转潜力
        rotation_candidates = 0
        for width, height in rectangles:
            if width != height and (width > self.canvas_width or height > self.canvas_width):
                rotation_candidates += 1

        log.info(f"可能受益于旋转的矩形: {rotation_candidates} 个")

        best_result = None
        test_count = 0
        total_tests = 3 * 2  # 3种排序 * 2种旋转设置

        # 测试参数组合，优先测试旋转启用的配置
        test_configs = []
        for sort_strategy in [0, 1, 2]:  # AREA, PERI, DIFF
            # 优先测试旋转启用
            test_configs.append((sort_strategy, 0, True))  # pack_algorithm设为0（默认）
            test_configs.append((sort_strategy, 0, False))

        for sort_strategy, pack_algorithm, rotation_enabled in test_configs:
            test_count += 1
            rotation_status = "启用" if rotation_enabled else "禁用"
            log.info(f"测试配置 {test_count}/{total_tests}: 排序={sort_strategy}, 算法={pack_algorithm}, 旋转={rotation_status}")

            result = self.test_rectpack_configuration(
                sort_strategy, pack_algorithm, rotation_enabled, rectangles
            )

            if result:
                log.info(f"  配置结果: 利用率 {result.utilization:.2f}%, 容器数 {result.containers_used}")

                if best_result is None or result.utilization > best_result.utilization:
                    best_result = result
                    log.info(f"🎯 发现更好的配置: 利用率 {result.utilization:.2f}%, 旋转={rotation_status}")
            else:
                log.warning(f"  配置测试失败")

        self.best_result = best_result
        return best_result

    def print_optimization_results(self, result: OptimizationResult):
        """打印优化结果"""
        if not result:
            log.error("没有有效的优化结果")
            return

        sort_names = ['AREA', 'PERIMETER', 'DIFFERENCE', 'SHORT_SIDE', 'LONG_SIDE', 'RATIO']
        pack_names = ['MaxRectsBssf', 'MaxRectsBaf', 'MaxRectsBlsf']

        print("\n" + "="*70)
        print("RectPack参数优化结果 (202cm宽 × 5000cm高，支持多容器)")
        print("="*70)
        print(f"最佳利用率: {result.utilization:.2f}%")
        print(f"使用容器数: {result.containers_used} 个")
        print(f"排序策略: {sort_names[result.params['sort_strategy']]}")
        print(f"装箱算法: {pack_names[result.params['pack_algorithm']]}")
        print(f"旋转启用: {result.params['rotation_enabled']}")
        print(f"总图片面积: {result.total_area:,.0f} cm²")
        print(f"实际使用面积: {result.container_area:,.0f} cm²")
        print(f"单容器面积: {self.canvas_width * self.canvas_height:,.0f} cm²")
        print(f"处理时间: {result.processing_time:.3f} 秒")
        print("="*70)

    def generate_config_updates(self, result: OptimizationResult) -> Dict[str, Any]:
        """生成配置更新"""
        if not result:
            return {}

        return {
            'rotation_enabled': result.params['rotation_enabled'],
            'sort_strategy': result.params['sort_strategy'],
            'pack_algorithm': result.params['pack_algorithm'],
            'optimization_iterations': 5,  # 保持用户偏好
            'min_utilization_threshold': 80.0,
            'rotation_penalty': 0.02,
            'aspect_ratio_preference': 1.2,
            'spacing_px': 1
        }

def main():
    """主函数"""
    # 测试数据
    test_data_text = """宽cm	高cm	数量
200	96	1
200	80	1
200	80	1
200	80	1
200	80	1
200	80	1
200	80	1
200	80	1
200	80	1
200	80	1
200	80	1
200	80	1
200	76	1
200	60	1
200	60	1
200	60	1
200	60	1
200	60	1
200	53	1
185	90	1
185	90	1
185	90	1
185	90	1
185	90	1
185	80	1
185	80	1
185	80	1
185	80	1
185	80	1
185	80	1
185	80	1
185	80	1
185	80	1
185	80	1
185	60	1
185	60	1
185	60	1
185	60	1
185	60	1
185	60	1
185	60	1
185	60	1
185	60	1
180	120	1
180	120	1
180	80	1
180	80	1
180	80	1
180	80	1
180	80	1
180	80	1
180	80	1
180	80	1
180	70	1
180	70	1
180	70	1
180	70	1
180	70	1
180	70	1
180	60	1
180	60	1
180	60	1
180	60	1
180	60	1
180	60	1
180	60	1
180	60	1
180	60	1
180	60	1
180	60	1
180	60	1
180	60	1
180	60	1
180	40	1
175	35	1
166	33	1
160	120	1
160	90	1
160	90	1
160	80	1
160	80	1
160	50	1
160	50	1
160	50	1
160	40	1
160	40	1
160	40	1
152	80	1
150	90	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
150	60	1
140	85	1
140	80	1
140	80	1
140	80	1
140	76	1
140	70	1
140	70	1
140	60	1
140	35	1
134	49	1
134	49	1
134	49	1
134	49	1
134	49	1
134	49	1
130	70	1
130	70	1
130	70	1
120	100	1
120	100	1
120	100	1
120	90	1
120	80	1
120	80	1
120	80	1
120	80	1
120	80	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	60	1
120	50	1
120	50	1
120	50	1
120	35	1
105	75	1
105	75	1
100	80	1
100	80	1
100	80	1
100	80	1
100	80	1
100	80	1
100	80	1
100	80	1
100	80	1
100	80	1
100	80	1
100	80	1
100	60	1
100	60	1
100	60	1
100	60	1
100	60	1
100	60	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	50	1
100	40	1
100	25	1
96	81	1
93	50	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	60	1
90	50	1
90	50	1
90	50	1
90	50	1
90	50	1
90	50	1
90	50	1
40	90	1
90	40	1
90	40	1
90	40	1
90	40	1
90	40	10
90	40	1
40	90	1
90	40	1
90	40	1
90	40	1
90	40	1
90	40	1
90	35	1
80	50	1
80	50	1
80	50	1
80	50	1
80	50	1
80	50	1
80	50	1
80	50	1
80	50	1
80	50	1
80	50	1
80	50	1
80	50	2
80	50	1
80	50	1
80	50	1
80	50	1
80	50	1
80	50	1
80	40	1
80	40	1
80	35	1
80	35	1
80	35	1
80	35	1
80	35	1
80	35	1
80	35	1
80	35	1
80	35	1
80	35	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
80	30	1
70	50	2
70	50	1
70	50	1
70	50	1
70	50	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	45	1
70	40	1
65	50	2
65	50	1
65	50	1
65	50	1
65	50	1
65	50	2
65	50	1
65	50	2
65	50	1
65	50	2
65	50	1
65	50	2
65	50	1
65	50	2
65	50	1
65	50	3
65	50	1
65	50	1
65	50	1
65	50	2
65	50	2
65	50	1
65	50	2
65	50	2
65	50	2
60	50	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
60	40	1
57	33	1
57	33	1
57	33	1
57	33	1
57	33	1
57	33	1
57	33	1
57	33	1
57	33	1
57	33	1
57	33	1
57	33	1
33	57	1
55	40	1
55	40	1
50	50	2
50	50	2
50	50	2
50	50	1
50	50	1
50	40	1
40	30	1
40	30	1
40	30	1
335	35	1
22	22	1"""

    # 创建优化器
    optimizer = RectPackParameterOptimizer()

    # 解析测试数据
    rectangles = optimizer.parse_test_data(test_data_text)

    # 执行优化
    best_result = optimizer.optimize_parameters(rectangles)

    # 打印结果
    optimizer.print_optimization_results(best_result)

    # 生成配置更新
    config_updates = optimizer.generate_config_updates(best_result)
    print("\n推荐的配置更新:")
    for key, value in config_updates.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    main()
