#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法问题诊断模块
第一步：识别测试模式和生产模式的关键差异
"""

import logging

log = logging.getLogger(__name__)

def diagnose_rectpack_differences():
    """诊断RectPack算法测试模式和生产模式的差异"""
    
    differences = {
        "算法参数": [],
        "单位转换": [],
        "容器处理": [],
        "图片旋转": [],
        "文档生成": []
    }
    
    # 检查算法参数差异
    try:
        # 测试模式参数
        from core.rectpack_test_mode import create_rectpack_config
        test_config = create_rectpack_config()
        
        # 生产模式参数 
        from core.rectpack_arranger import RectPackArranger
        
        differences["算法参数"].append("测试模式使用SORT_AREA排序")
        differences["算法参数"].append("生产模式使用复杂排序策略")
        
    except Exception as e:
        differences["算法参数"].append(f"参数检查失败: {str(e)}")
    
    # 检查单位转换差异
    differences["单位转换"].append("测试模式: cm直接转px (1:1)")
    differences["单位转换"].append("生产模式: cm转px使用PPI")
    
    # 检查容器处理差异
    differences["容器处理"].append("测试模式: 多容器支持")
    differences["容器处理"].append("生产模式: 单容器处理")
    
    # 检查图片旋转差异
    differences["图片旋转"].append("测试模式: 标记旋转状态")
    differences["图片旋转"].append("生产模式: PS中旋转整个画布")
    
    # 检查文档生成差异
    differences["文档生成"].append("测试模式: 生成TXT文档")
    differences["文档生成"].append("生产模式: 只生成TIFF文档")
    
    return differences

def log_diagnostic_results():
    """记录诊断结果"""
    log.info("=== RectPack算法差异诊断 ===")
    
    differences = diagnose_rectpack_differences()
    
    for category, issues in differences.items():
        log.info(f"\n{category}:")
        for issue in issues:
            log.info(f"  - {issue}")
    
    log.info("\n=== 诊断完成 ===")
    return differences

if __name__ == "__main__":
    log_diagnostic_results()
