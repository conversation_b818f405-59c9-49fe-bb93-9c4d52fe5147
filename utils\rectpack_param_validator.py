#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法参数验证工具
用于验证高级设置参数是否正确传递到算法执行中
"""

import logging
import json
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass

log = logging.getLogger(__name__)


@dataclass
class ParameterValidationResult:
    """参数验证结果"""
    parameter_name: str
    config_value: Any
    ui_value: Any
    algorithm_value: Any
    is_consistent: bool
    error_message: str = ""


class RectPackParameterValidator:
    """RectPack参数验证器"""
    
    def __init__(self, config_manager=None):
        """
        初始化参数验证器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.validation_results = []
        
        # 定义需要验证的参数列表
        self.parameters_to_validate = {
            # 核心算法参数
            'rectpack_rotation_enabled': {'type': bool, 'default': True},
            'rectpack_sort_strategy': {'type': int, 'default': 0, 'range': (0, 5)},
            'rectpack_pack_algorithm': {'type': int, 'default': 0, 'range': (0, 2)},
            
            # 优化参数
            'rectpack_enable_optimization': {'type': bool, 'default': True},
            'rectpack_optimization_iterations': {'type': int, 'default': 5, 'range': (1, 100)},
            'rectpack_min_utilization_threshold': {'type': float, 'default': 80.0, 'range': (50.0, 100.0)},
            'rectpack_rotation_penalty': {'type': float, 'default': 0.02, 'range': (0.0, 1.0)},
            'rectpack_aspect_ratio_preference': {'type': float, 'default': 1.2, 'range': (0.1, 10.0)},
            
            # 高级参数
            'rectpack_bin_selection_strategy': {'type': int, 'default': 0, 'range': (0, 2)},
            'rectpack_split_heuristic': {'type': int, 'default': 0, 'range': (0, 2)},
            'rectpack_free_rect_choice': {'type': int, 'default': 0, 'range': (0, 2)},
            
            # 性能参数
            'rectpack_max_processing_time': {'type': int, 'default': 300, 'range': (60, 3600)},
            'rectpack_batch_size': {'type': int, 'default': 100, 'range': (10, 1000)},
            'rectpack_memory_limit_mb': {'type': int, 'default': 512, 'range': (128, 4096)},
            'rectpack_enable_parallel': {'type': bool, 'default': False},
            
            # 调试参数
            'rectpack_debug_mode': {'type': bool, 'default': False},
            'rectpack_log_level': {'type': int, 'default': 1, 'range': (0, 3)},
            'rectpack_save_intermediate_results': {'type': bool, 'default': False},
            'rectpack_visualization_enabled': {'type': bool, 'default': False}
        }
    
    def validate_config_database(self) -> Dict[str, Any]:
        """验证配置数据库中的参数"""
        config_values = {}
        
        if not self.config_manager:
            log.error("配置管理器未设置，无法验证配置数据库")
            return config_values
        
        try:
            for param_name, param_info in self.parameters_to_validate.items():
                value = self.config_manager.get(param_name, param_info['default'])
                config_values[param_name] = value
                
                # 验证参数类型
                expected_type = param_info['type']
                if not isinstance(value, expected_type):
                    log.warning(f"参数 {param_name} 类型不匹配: 期望 {expected_type.__name__}, 实际 {type(value).__name__}")
                
                # 验证参数范围
                if 'range' in param_info:
                    min_val, max_val = param_info['range']
                    if not (min_val <= value <= max_val):
                        log.warning(f"参数 {param_name} 超出范围: {value} 不在 [{min_val}, {max_val}] 内")
            
            log.info(f"配置数据库验证完成，共验证 {len(config_values)} 个参数")
            
        except Exception as e:
            log.error(f"验证配置数据库失败: {str(e)}")
        
        return config_values
    
    def validate_ui_settings(self, settings_dialog=None) -> Dict[str, Any]:
        """验证UI界面设置"""
        ui_values = {}
        
        if not settings_dialog:
            log.warning("设置对话框未提供，跳过UI验证")
            return ui_values
        
        try:
            # 从设置对话框获取当前值
            settings = settings_dialog.get_settings()
            
            for param_name in self.parameters_to_validate.keys():
                if param_name in settings:
                    ui_values[param_name] = settings[param_name]
            
            log.info(f"UI设置验证完成，共获取 {len(ui_values)} 个参数")
            
        except Exception as e:
            log.error(f"验证UI设置失败: {str(e)}")
        
        return ui_values
    
    def validate_algorithm_parameters(self, arranger=None) -> Dict[str, Any]:
        """验证算法实际使用的参数"""
        algorithm_values = {}
        
        if not arranger:
            log.warning("排列器未提供，跳过算法参数验证")
            return algorithm_values
        
        try:
            # 从排列器获取实际使用的参数
            if hasattr(arranger, 'get_current_parameters'):
                algorithm_values = arranger.get_current_parameters()
            else:
                log.warning("排列器不支持参数获取方法")
            
            log.info(f"算法参数验证完成，共获取 {len(algorithm_values)} 个参数")
            
        except Exception as e:
            log.error(f"验证算法参数失败: {str(e)}")
        
        return algorithm_values
    
    def compare_parameters(self, config_values: Dict[str, Any], 
                          ui_values: Dict[str, Any], 
                          algorithm_values: Dict[str, Any]) -> List[ParameterValidationResult]:
        """比较不同来源的参数值"""
        results = []
        
        for param_name in self.parameters_to_validate.keys():
            config_val = config_values.get(param_name, "未找到")
            ui_val = ui_values.get(param_name, "未找到")
            algo_val = algorithm_values.get(param_name, "未找到")
            
            # 检查一致性
            is_consistent = True
            error_msg = ""
            
            if config_val != "未找到" and ui_val != "未找到":
                if config_val != ui_val:
                    is_consistent = False
                    error_msg += f"配置与UI不一致; "
            
            if config_val != "未找到" and algo_val != "未找到":
                if config_val != algo_val:
                    is_consistent = False
                    error_msg += f"配置与算法不一致; "
            
            if ui_val != "未找到" and algo_val != "未找到":
                if ui_val != algo_val:
                    is_consistent = False
                    error_msg += f"UI与算法不一致; "
            
            result = ParameterValidationResult(
                parameter_name=param_name,
                config_value=config_val,
                ui_value=ui_val,
                algorithm_value=algo_val,
                is_consistent=is_consistent,
                error_message=error_msg.strip("; ")
            )
            
            results.append(result)
        
        return results
    
    def generate_validation_report(self, results: List[ParameterValidationResult]) -> str:
        """生成验证报告"""
        report_lines = [
            "# RectPack参数验证报告",
            "",
            f"验证时间: {log.handlers[0].formatter.formatTime(log.makeRecord('', 0, '', 0, '', (), None)) if log.handlers else '未知'}",
            f"验证参数数量: {len(results)}",
            ""
        ]
        
        # 统计信息
        consistent_count = sum(1 for r in results if r.is_consistent)
        inconsistent_count = len(results) - consistent_count
        
        report_lines.extend([
            "## 验证统计",
            f"- 一致参数: {consistent_count}",
            f"- 不一致参数: {inconsistent_count}",
            f"- 一致性比例: {consistent_count/len(results)*100:.1f}%",
            ""
        ])
        
        # 详细结果
        report_lines.extend([
            "## 详细验证结果",
            "",
            "| 参数名 | 配置值 | UI值 | 算法值 | 一致性 | 错误信息 |",
            "|--------|--------|------|--------|--------|----------|"
        ])
        
        for result in results:
            status = "✅" if result.is_consistent else "❌"
            report_lines.append(
                f"| {result.parameter_name} | {result.config_value} | {result.ui_value} | "
                f"{result.algorithm_value} | {status} | {result.error_message} |"
            )
        
        # 不一致参数的详细分析
        if inconsistent_count > 0:
            report_lines.extend([
                "",
                "## 不一致参数分析",
                ""
            ])
            
            for result in results:
                if not result.is_consistent:
                    report_lines.extend([
                        f"### {result.parameter_name}",
                        f"- 配置值: {result.config_value}",
                        f"- UI值: {result.ui_value}",
                        f"- 算法值: {result.algorithm_value}",
                        f"- 问题: {result.error_message}",
                        ""
                    ])
        
        return "\n".join(report_lines)
    
    def run_full_validation(self, settings_dialog=None, arranger=None) -> str:
        """运行完整的参数验证"""
        log.info("开始RectPack参数验证...")
        
        # 验证各个来源的参数
        config_values = self.validate_config_database()
        ui_values = self.validate_ui_settings(settings_dialog)
        algorithm_values = self.validate_algorithm_parameters(arranger)
        
        # 比较参数
        results = self.compare_parameters(config_values, ui_values, algorithm_values)
        
        # 生成报告
        report = self.generate_validation_report(results)
        
        log.info("RectPack参数验证完成")
        return report


def validate_rectpack_parameters(config_manager=None, settings_dialog=None, arranger=None) -> str:
    """
    便捷函数：验证RectPack参数
    
    Args:
        config_manager: 配置管理器
        settings_dialog: 设置对话框
        arranger: 排列器
    
    Returns:
        str: 验证报告
    """
    validator = RectPackParameterValidator(config_manager)
    return validator.run_full_validation(settings_dialog, arranger)


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 创建验证器并运行测试
    validator = RectPackParameterValidator()
    
    # 模拟一些测试数据
    test_config = {
        'rectpack_rotation_enabled': True,
        'rectpack_optimization_iterations': 5
    }
    
    test_ui = {
        'rectpack_rotation_enabled': True,
        'rectpack_optimization_iterations': 3  # 故意不一致
    }
    
    test_algo = {
        'rectpack_rotation_enabled': False,  # 故意不一致
        'rectpack_optimization_iterations': 5
    }
    
    results = validator.compare_parameters(test_config, test_ui, test_algo)
    report = validator.generate_validation_report(results)
    
    print(report)
