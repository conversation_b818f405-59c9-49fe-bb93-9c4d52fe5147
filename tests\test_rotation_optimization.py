#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
旋转功能优化测试
专门测试rectpack算法的旋转参数效果
"""

import logging
import time
from typing import List, Tuple, Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def test_rotation_effect():
    """测试旋转功能的效果"""
    
    # 测试数据 - 选择一些明显受益于旋转的矩形
    test_rectangles = [
        # 大尺寸矩形 - 可能需要旋转
        (200, 96), (200, 80), (200, 76), (200, 60), (200, 53),
        (185, 90), (185, 80), (185, 60),
        (180, 120), (180, 80), (180, 70), (180, 60),
        (160, 120), (160, 90), (160, 80),
        (150, 90), (150, 60),
        
        # 中等尺寸矩形
        (140, 85), (140, 80), (140, 76), (140, 70),
        (130, 70),
        (120, 100), (120, 90), (120, 80), (120, 60),
        
        # 小尺寸矩形
        (100, 80), (100, 60), (100, 50),
        (90, 60), (90, 50), (90, 40),
        (80, 50), (80, 40), (80, 35), (80, 30),
        (70, 50), (70, 45),
        (65, 50), (60, 40), (57, 33), (50, 50), (50, 40)
    ]
    
    canvas_width = 202
    canvas_height = 5000
    
    log.info(f"测试旋转功能效果")
    log.info(f"画布尺寸: {canvas_width}cm × {canvas_height}cm")
    log.info(f"测试矩形数量: {len(test_rectangles)}")
    
    try:
        from rectpack import newPacker, SORT_AREA
        from rectpack import MaxRectsBssf
        
        # 测试不启用旋转
        log.info("\n=== 测试1: 不启用旋转 ===")
        result_no_rotation = test_single_configuration(
            test_rectangles, canvas_width, canvas_height, 
            rotation_enabled=False, algorithm=MaxRectsBssf, sort_algo=SORT_AREA
        )
        
        # 测试启用旋转
        log.info("\n=== 测试2: 启用旋转 ===")
        result_with_rotation = test_single_configuration(
            test_rectangles, canvas_width, canvas_height, 
            rotation_enabled=True, algorithm=MaxRectsBssf, sort_algo=SORT_AREA
        )
        
        # 比较结果
        log.info("\n" + "="*60)
        log.info("旋转功能效果对比")
        log.info("="*60)
        
        if result_no_rotation and result_with_rotation:
            log.info(f"不启用旋转:")
            log.info(f"  利用率: {result_no_rotation['utilization']:.2f}%")
            log.info(f"  容器数: {result_no_rotation['containers_used']}")
            log.info(f"  实际使用面积: {result_no_rotation['used_area']:,.0f} cm²")
            
            log.info(f"启用旋转:")
            log.info(f"  利用率: {result_with_rotation['utilization']:.2f}%")
            log.info(f"  容器数: {result_with_rotation['containers_used']}")
            log.info(f"  实际使用面积: {result_with_rotation['used_area']:,.0f} cm²")
            
            improvement = result_with_rotation['utilization'] - result_no_rotation['utilization']
            log.info(f"旋转功能改善: {improvement:+.2f}%")
            
            if improvement > 0:
                log.info("🎯 旋转功能有效，建议启用")
                return True
            else:
                log.info("⚠️ 旋转功能无明显改善")
                return False
        else:
            log.error("测试失败，无法比较结果")
            return False
            
    except ImportError:
        log.error("RectPack库不可用")
        return False
    except Exception as e:
        log.error(f"测试失败: {str(e)}")
        return False

def test_single_configuration(rectangles, canvas_width, canvas_height, 
                            rotation_enabled, algorithm, sort_algo):
    """测试单个配置"""
    try:
        from rectpack import newPacker
        
        containers_used = 0
        total_area = sum(w * h for w, h in rectangles)
        total_used_area = 0
        remaining_rectangles = rectangles.copy()
        
        while remaining_rectangles and containers_used < 10:  # 最多10个容器
            containers_used += 1
            
            # 创建装箱器
            packer = newPacker(
                pack_algo=algorithm,
                sort_algo=sort_algo,
                rotation=rotation_enabled
            )
            
            packer.add_bin(canvas_width, canvas_height)
            
            # 添加矩形
            for i, (width, height) in enumerate(remaining_rectangles):
                packer.add_rect(width, height, rid=i)
            
            # 执行装箱
            packer.pack()
            
            # 检查放置结果
            placed_indices = set()
            max_y = 0
            
            for bin_id in packer:
                for rect in packer[bin_id]:
                    placed_indices.add(rect.rid)
                    max_y = max(max_y, rect.y + rect.height)
            
            if not placed_indices:
                break
            
            # 计算当前容器使用面积
            current_used_area = canvas_width * max_y
            total_used_area += current_used_area
            
            # 移除已放置的矩形
            remaining_rectangles = [rect for i, rect in enumerate(remaining_rectangles) 
                                  if i not in placed_indices]
            
            log.info(f"  容器 {containers_used}: 放置 {len(placed_indices)} 个矩形, 高度 {max_y}cm")
        
        utilization = (total_area / total_used_area) * 100 if total_used_area > 0 else 0
        
        return {
            'utilization': utilization,
            'containers_used': containers_used,
            'used_area': total_used_area,
            'total_area': total_area
        }
        
    except Exception as e:
        log.error(f"配置测试失败: {str(e)}")
        return None

def get_optimal_rotation_config():
    """获取最优的旋转配置"""
    rotation_effective = test_rotation_effect()
    
    # 基于测试结果返回推荐配置
    optimal_config = {
        'rotation_enabled': rotation_effective,  # 根据测试结果决定
        'sort_strategy': 0,  # AREA排序通常效果最好
        'pack_algorithm': 0,  # MaxRectsBssf算法
        'optimization_iterations': 5,
        'min_utilization_threshold': 80.0,
        'rotation_penalty': 0.02 if rotation_effective else 0.0,
        'aspect_ratio_preference': 1.2,
        'spacing_px': 1
    }
    
    log.info("\n推荐的最优配置:")
    for key, value in optimal_config.items():
        log.info(f"  {key}: {value}")
    
    return optimal_config

if __name__ == "__main__":
    log.info("开始旋转功能优化测试...")
    optimal_config = get_optimal_rotation_config()
    
    if optimal_config['rotation_enabled']:
        log.info("\n✅ 建议启用旋转功能以提高画布利用率")
    else:
        log.info("\n⚠️ 旋转功能对当前数据集无明显改善，可选择性启用")
