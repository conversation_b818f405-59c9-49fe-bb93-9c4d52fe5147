#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试画布优化功能
验证93%利用率目标和画布高度截断逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.rectpack_arranger import RectPackArranger

class TestLogger:
    """测试日志记录器"""

    def __init__(self):
        pass

    def emit(self, message):
        print(f"[LOG] {message}")

def test_canvas_optimization():
    """测试画布优化功能"""
    print("=" * 60)
    print("测试画布优化功能")
    print("=" * 60)
    
    # 创建测试日志器
    logger = TestLogger()
    
    # 创建RectPack排列器
    # 画布宽度: 163cm = 4633px (72 PPI)
    # 最大高度: 500cm = 14173px (72 PPI)
    canvas_width_px = int(163 * 72 / 2.54)  # 163cm转像素
    max_height_px = int(500 * 72 / 2.54)    # 500cm转像素
    
    arranger = RectPackArranger(
        container_width=canvas_width_px,
        image_spacing=10,
        max_height=max_height_px,
        log_signal=logger
    )
    
    print(f"画布设置: {canvas_width_px}x{max_height_px}px")
    print(f"目标利用率: {arranger.target_utilization}%")
    
    # 测试图片数据 - 模拟137x51cm的图片
    test_images = []
    image_width_px = int(137 * 72 / 2.54)   # 137cm转像素
    image_height_px = int(51 * 72 / 2.54)   # 51cm转像素
    
    print(f"测试图片尺寸: {image_width_px}x{image_height_px}px (137x51cm)")
    
    # 创建15张相同尺寸的图片
    for i in range(15):
        test_images.append({
            'width': image_width_px,
            'height': image_height_px,
            'name': f'test_image_{i+1}',
            'path': f'/test/image_{i+1}.jpg'
        })
    
    print(f"准备放置 {len(test_images)} 张图片")
    print("-" * 40)
    
    # 逐个放置图片
    placed_count = 0
    for i, img in enumerate(test_images):
        x, y, success = arranger.place_image(
            img['width'], 
            img['height'], 
            img
        )
        
        if success:
            placed_count += 1
            print(f"✅ 图片 {i+1} 放置成功: ({x}, {y})")
        else:
            print(f"❌ 图片 {i+1} 放置失败")
            break
        
        # 检查画布状态
        if arranger.canvas_is_full:
            print(f"📊 画布已满，已放置 {placed_count} 张图片")
            break
    
    print("-" * 40)
    
    # 获取最终统计信息
    stats = arranger.get_layout_info()
    print("最终统计信息:")
    print(f"  画布尺寸: {stats['container_width']}x{stats['container_height']}px")
    print(f"  利用率: {stats['utilization_percent']:.2f}%")
    print(f"  已放置图片: {stats['placed_count']} 张")
    print(f"  使用面积: {stats['used_area']} px²")
    print(f"  总面积: {stats['total_area']} px²")
    print(f"  画布已满: {'是' if stats['canvas_is_full'] else '否'}")
    
    # 验证优化目标
    print("-" * 40)
    print("优化目标验证:")
    
    # 1. 验证利用率目标
    if stats['utilization_percent'] >= 93.0:
        print(f"✅ 利用率目标达成: {stats['utilization_percent']:.2f}% >= 93%")
    else:
        print(f"⚠️ 利用率未达标: {stats['utilization_percent']:.2f}% < 93%")
    
    # 2. 验证画布高度截断
    if arranger.canvas_is_full:
        print("✅ 画布已满，高度已截断到最底部图片")
    else:
        print("⚠️ 画布未满，可能还有空间")
    
    # 3. 验证配置读取
    print(f"✅ 最大高度限制: {arranger.bin_height}px ({arranger.bin_height * 2.54 / 72:.1f}cm)")
    
    print("=" * 60)
    print("测试完成")
    print("=" * 60)
    
    return stats

def test_utilization_threshold():
    """测试利用率阈值功能"""
    print("\n" + "=" * 60)
    print("测试利用率阈值功能")
    print("=" * 60)
    
    logger = TestLogger()
    
    # 创建较小的画布进行测试
    canvas_width_px = 1000
    max_height_px = 1000
    
    arranger = RectPackArranger(
        container_width=canvas_width_px,
        image_spacing=5,
        max_height=max_height_px,
        log_signal=logger
    )
    
    # 设置较低的目标利用率进行测试
    arranger.set_target_utilization(80.0)
    print(f"设置目标利用率: {arranger.target_utilization}%")
    
    # 放置一些图片
    test_images = [
        {'width': 400, 'height': 300, 'name': 'img1'},
        {'width': 500, 'height': 200, 'name': 'img2'},
        {'width': 300, 'height': 400, 'name': 'img3'},
    ]
    
    for img in test_images:
        x, y, success = arranger.place_image(img['width'], img['height'], img)
        if not success:
            break
    
    # 检查利用率检查功能
    target_reached = arranger._check_utilization_target_reached()
    should_be_full = arranger._should_canvas_be_considered_full()
    
    stats = arranger.get_layout_info()
    print(f"当前利用率: {stats['utilization_percent']:.2f}%")
    print(f"目标利用率达成: {'是' if target_reached else '否'}")
    print(f"应该认为画布已满: {'是' if should_be_full else '否'}")
    
    print("=" * 60)

if __name__ == "__main__":
    # 运行测试
    stats = test_canvas_optimization()
    test_utilization_threshold()
    
    print(f"\n总结:")
    print(f"- 利用率: {stats['utilization_percent']:.2f}%")
    print(f"- 目标达成: {'✅' if stats['utilization_percent'] >= 93.0 else '❌'}")
    print(f"- 画布优化: {'✅' if stats['canvas_is_full'] else '⚠️'}")
