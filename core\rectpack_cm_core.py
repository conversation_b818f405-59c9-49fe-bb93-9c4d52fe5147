#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法CM单位核心模块
整个计算过程都使用cm单位，只在PS排列时转换为px单位
"""

import logging
import time
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import copy

log = logging.getLogger(__name__)

@dataclass
class ImageDataCM:
    """图片数据（cm单位）"""
    id: str
    width_cm: float
    height_cm: float
    name: str
    path: str = ""
    area_cm2: float = 0.0
    aspect_ratio: float = 1.0

    def __post_init__(self):
        self.area_cm2 = self.width_cm * self.height_cm
        self.aspect_ratio = self.width_cm / self.height_cm if self.height_cm > 0 else 1.0

@dataclass
class PlacementResultCM:
    """放置结果（cm单位）"""
    x_cm: float
    y_cm: float
    width_cm: float
    height_cm: float
    rotated: bool
    image_id: str

@dataclass
class CanvasResultCM:
    """画布结果（cm单位）"""
    canvas_index: int
    width_cm: float
    height_cm: float
    placements: List[PlacementResultCM]
    utilization_rate: float
    area_used_cm2: float
    canvas_area_cm2: float

class RectPackCMCore:
    """RectPack算法CM单位核心类"""

    def __init__(self, canvas_width_cm: float = 163.0, max_height_cm: float = 5000.0):
        """
        初始化RectPack CM核心

        Args:
            canvas_width_cm: 画布宽度（cm）
            max_height_cm: 最大高度（cm）
        """
        self.canvas_width_cm = canvas_width_cm
        self.max_height_cm = max_height_cm

        # 从配置加载最优参数
        self.params = self._load_optimal_params()

        log.info(f"RectPack CM核心初始化: {canvas_width_cm}cm × {max_height_cm}cm")

    def _load_optimal_params(self) -> Dict[str, Any]:
        """加载最优参数配置"""
        try:
            from utils.config_manager_duckdb import ConfigManagerDuckDB
            config_manager = ConfigManagerDuckDB()
            rectpack_settings = config_manager.get_rectpack_settings()

            return {
                'rotation_enabled': rectpack_settings.get('rectpack_rotation_enabled', True),
                'sort_strategy': rectpack_settings.get('rectpack_sort_strategy', 3),  # 短边排序
                'pack_algorithm': rectpack_settings.get('rectpack_pack_algorithm', 1),  # BFF
                'spacing_cm': rectpack_settings.get('rectpack_spacing_px', 1) * 2.54 / 72,  # px转cm
                'edge_shrink_tolerance_cm': rectpack_settings.get('rectpack_edge_shrink_tolerance_cm', 0.8),
                'enable_edge_shrink': rectpack_settings.get('rectpack_enable_edge_shrink', True),
                'enable_gap_filling': rectpack_settings.get('rectpack_enable_gap_filling', True),
                'enable_adaptive_sorting': rectpack_settings.get('rectpack_enable_adaptive_sorting', True),
                'enable_multi_stage_optimization': rectpack_settings.get('rectpack_enable_multi_stage_optimization', True)
            }
        except Exception as e:
            log.warning(f"加载配置失败，使用默认参数: {str(e)}")
            return {
                'rotation_enabled': True,
                'sort_strategy': 3,  # 短边排序
                'pack_algorithm': 1,  # BFF
                'spacing_cm': 1 * 2.54 / 72,  # 1px转cm
                'edge_shrink_tolerance_cm': 0.8,
                'enable_edge_shrink': True,
                'enable_gap_filling': True,
                'enable_adaptive_sorting': True,
                'enable_multi_stage_optimization': True
            }

    def arrange_images_cm(self, images_data: List[Dict[str, Any]]) -> List[CanvasResultCM]:
        """
        使用cm单位排列图片

        Args:
            images_data: 图片数据列表，格式：[{'width_cm': float, 'height_cm': float, 'name': str, 'path': str}, ...]

        Returns:
            List[CanvasResultCM]: 画布结果列表
        """
        log.info(f"开始CM单位图片排列，共{len(images_data)}张图片")
        start_time = time.time()

        # 准备图片数据
        images_cm = self._prepare_images_cm(images_data)

        # 执行排列
        canvas_results = self._execute_packing_cm(images_cm)

        processing_time = time.time() - start_time
        log.info(f"CM单位排列完成，耗时{processing_time:.2f}秒，生成{len(canvas_results)}个画布")

        return canvas_results

    def _prepare_images_cm(self, images_data: List[Dict[str, Any]]) -> List[ImageDataCM]:
        """准备CM单位图片数据"""
        images_cm = []

        for i, img_data in enumerate(images_data):
            image_cm = ImageDataCM(
                id=str(i + 1),
                width_cm=float(img_data['width_cm']),
                height_cm=float(img_data['height_cm']),
                name=img_data.get('name', f'image_{i+1}'),
                path=img_data.get('path', '')
            )
            images_cm.append(image_cm)

        log.info(f"准备了{len(images_cm)}张CM单位图片数据")
        return images_cm

    def _execute_packing_cm(self, images_cm: List[ImageDataCM]) -> List[CanvasResultCM]:
        """执行CM单位装箱"""
        try:
            from rectpack import newPacker, SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO
            from rectpack import MaxRectsBssf, MaxRectsBaf, MaxRectsBlsf

            # 算法映射
            sort_algos = [SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO]
            pack_algos = [MaxRectsBssf, MaxRectsBaf, MaxRectsBlsf]

            canvas_results = []
            remaining_images = copy.deepcopy(images_cm)
            canvas_index = 0

            while remaining_images and canvas_index < 20:  # 最多20个画布
                log.info(f"开始处理第{canvas_index + 1}个画布，剩余{len(remaining_images)}张图片")

                # 创建装箱器
                packer = newPacker(
                    pack_algo=pack_algos[self.params['pack_algorithm']],
                    sort_algo=sort_algos[self.params['sort_strategy']],
                    rotation=self.params['rotation_enabled']
                )

                # 添加容器（使用cm单位）
                packer.add_bin(self.canvas_width_cm, self.max_height_cm)

                # 添加图片（使用cm单位）
                for img in remaining_images:
                    width_cm = img.width_cm
                    height_cm = img.height_cm

                    # 应用边长缩减
                    if self.params['enable_edge_shrink']:
                        shrink_cm = self.params['edge_shrink_tolerance_cm']
                        width_cm = max(0.1, width_cm - shrink_cm)
                        height_cm = max(0.1, height_cm - shrink_cm)

                    # 添加间距
                    width_cm += self.params['spacing_cm']
                    height_cm += self.params['spacing_cm']

                    packer.add_rect(width_cm, height_cm, rid=img.id)

                # 执行装箱
                packer.pack()

                # 处理结果
                canvas_result = self._process_packing_result_cm(packer, remaining_images, canvas_index)

                if canvas_result and canvas_result.placements:
                    canvas_results.append(canvas_result)

                    # 移除已放置的图片
                    placed_ids = {p.image_id for p in canvas_result.placements}
                    remaining_images = [img for img in remaining_images if img.id not in placed_ids]

                    log.info(f"第{canvas_index + 1}个画布完成，放置{len(canvas_result.placements)}张图片，"
                            f"利用率{canvas_result.utilization_rate:.2f}%")
                else:
                    log.warning(f"第{canvas_index + 1}个画布无法放置任何图片，停止处理")
                    break

                canvas_index += 1

            if remaining_images:
                log.warning(f"还有{len(remaining_images)}张图片未能放置")

            return canvas_results

        except Exception as e:
            log.error(f"CM单位装箱执行失败: {str(e)}")
            return []

    def _process_packing_result_cm(self, packer, images_cm: List[ImageDataCM],
                                  canvas_index: int) -> Optional[CanvasResultCM]:
        """处理装箱结果（cm单位）"""
        placements = []
        max_y_cm = 0.0
        total_area_cm2 = 0.0

        # 创建图片ID到图片对象的映射
        image_map = {img.id: img for img in images_cm}

        for rect in packer.rect_list():
            # RectPack返回的是元组: (bin_id, x, y, width, height, rid)
            bin_id, x_cm, y_cm, width_cm, height_cm, image_id = rect

            if bin_id == 0:  # 当前画布
                # 检查是否旋转
                original_img = image_map.get(image_id)
                if original_img:
                    # 判断是否旋转：比较原始尺寸和放置尺寸
                    original_width = original_img.width_cm
                    original_height = original_img.height_cm

                    # 考虑边长缩减和间距
                    if self.params['enable_edge_shrink']:
                        shrink_cm = self.params['edge_shrink_tolerance_cm']
                        original_width = max(0.1, original_width - shrink_cm)
                        original_height = max(0.1, original_height - shrink_cm)

                    original_width += self.params['spacing_cm']
                    original_height += self.params['spacing_cm']

                    # 判断是否旋转（允许小误差）
                    rotated = (abs(width_cm - original_height) < 0.01 and
                              abs(height_cm - original_width) < 0.01)

                    placement = PlacementResultCM(
                        x_cm=x_cm,
                        y_cm=y_cm,
                        width_cm=width_cm,
                        height_cm=height_cm,
                        rotated=rotated,
                        image_id=image_id
                    )
                    placements.append(placement)

                    max_y_cm = max(max_y_cm, y_cm + height_cm)
                    total_area_cm2 += width_cm * height_cm

        if not placements:
            return None

        # 计算利用率
        canvas_area_cm2 = self.canvas_width_cm * max_y_cm
        utilization_rate = (total_area_cm2 / canvas_area_cm2 * 100) if canvas_area_cm2 > 0 else 0

        return CanvasResultCM(
            canvas_index=canvas_index,
            width_cm=self.canvas_width_cm,
            height_cm=max_y_cm,
            placements=placements,
            utilization_rate=utilization_rate,
            area_used_cm2=total_area_cm2,
            canvas_area_cm2=canvas_area_cm2
        )

    def convert_to_px_for_ps(self, canvas_results_cm: List[CanvasResultCM],
                           ppi: int = 72) -> List[Dict[str, Any]]:
        """
        将CM单位结果转换为PX单位，用于PS处理

        Args:
            canvas_results_cm: CM单位画布结果
            ppi: 像素密度

        Returns:
            List[Dict]: PX单位结果，用于PS处理
        """
        log.info(f"开始转换CM单位结果为PX单位，PPI={ppi}")

        px_results = []
        cm_to_px_ratio = ppi / 2.54  # 1cm = ppi/2.54 px

        for canvas_cm in canvas_results_cm:
            # 转换画布尺寸
            canvas_width_px = int(round(canvas_cm.width_cm * cm_to_px_ratio))
            canvas_height_px = int(round(canvas_cm.height_cm * cm_to_px_ratio))

            # 转换图片放置信息
            placements_px = []
            for placement_cm in canvas_cm.placements:
                placement_px = {
                    'x': int(round(placement_cm.x_cm * cm_to_px_ratio)),
                    'y': int(round(placement_cm.y_cm * cm_to_px_ratio)),
                    'width': int(round(placement_cm.width_cm * cm_to_px_ratio)),
                    'height': int(round(placement_cm.height_cm * cm_to_px_ratio)),
                    'rotated': placement_cm.rotated,
                    'image_id': placement_cm.image_id
                }
                placements_px.append(placement_px)

            canvas_px = {
                'canvas_index': canvas_cm.canvas_index,
                'width_px': canvas_width_px,
                'height_px': canvas_height_px,
                'placements': placements_px,
                'utilization_rate': canvas_cm.utilization_rate,
                'area_used_px2': int(round(canvas_cm.area_used_cm2 * (cm_to_px_ratio ** 2))),
                'canvas_area_px2': int(round(canvas_cm.canvas_area_cm2 * (cm_to_px_ratio ** 2)))
            }
            px_results.append(canvas_px)

        log.info(f"CM到PX转换完成，转换了{len(px_results)}个画布")
        return px_results

    def convert_to_px_for_ps_enhanced(self, canvas_results_cm: List[CanvasResultCM],
                                    ppi: int = 72) -> List[Dict[str, Any]]:
        """
        高精度CM单位结果转换为PX单位，用于PS处理
        解决测试模式和生产模式利用率差异问题

        Args:
            canvas_results_cm: CM单位画布结果
            ppi: 像素密度

        Returns:
            List[Dict]: 高精度PX单位结果，用于PS处理
        """
        log.info(f"开始高精度转换CM单位结果为PX单位，PPI={ppi}")

        try:
            from core.rectpack_high_precision_converter import HighPrecisionConverter

            # 创建高精度转换器
            converter = HighPrecisionConverter(ppi)

            # 执行高精度转换
            px_results = converter.convert_results_precise(canvas_results_cm, ppi)

            # 添加兼容性字段，确保与现有代码兼容
            for px_canvas in px_results:
                # 为PS调用准备标准字段
                px_canvas['width_px'] = px_canvas['width_px_rounded']
                px_canvas['height_px'] = px_canvas['height_px_rounded']

                # 转换放置信息为标准格式
                standard_placements = []
                for placement_precise in px_canvas['placements_precise']:
                    standard_placement = {
                        'x': placement_precise['x_rounded'],
                        'y': placement_precise['y_rounded'],
                        'width': placement_precise['width_rounded'],
                        'height': placement_precise['height_rounded'],
                        'rotated': placement_precise['rotated'],
                        'image_id': placement_precise['image_id'],
                        # 保留高精度数据用于误差补偿
                        'x_precise': float(placement_precise['x_precise']),
                        'y_precise': float(placement_precise['y_precise']),
                        'width_precise': float(placement_precise['width_precise']),
                        'height_precise': float(placement_precise['height_precise'])
                    }
                    standard_placements.append(standard_placement)

                px_canvas['placements'] = standard_placements

                # 使用高精度利用率
                px_canvas['utilization_rate'] = px_canvas['utilization_rate_precise']

                # 添加精度改进标记
                px_canvas['precision_enhanced'] = True
                px_canvas['conversion_method'] = 'high_precision'

            log.info(f"高精度CM到PX转换完成，转换了{len(px_results)}个画布")

            # 记录精度改进效果
            if px_results:
                overall_utilization = px_results[0].get('overall_utilization_precise', 0)
                log.info(f"高精度转换整体利用率: {overall_utilization:.2f}%")

            return px_results

        except ImportError:
            log.warning("高精度转换器不可用，回退到标准转换")
            return self.convert_to_px_for_ps(canvas_results_cm, ppi)
        except Exception as e:
            log.error(f"高精度转换失败: {str(e)}，回退到标准转换")
            return self.convert_to_px_for_ps(canvas_results_cm, ppi)

    def generate_test_mode_visualization(self, canvas_results_cm: List[CanvasResultCM]) -> List[Dict[str, Any]]:
        """
        生成测试模式可视化数据

        Args:
            canvas_results_cm: CM单位画布结果

        Returns:
            List[Dict]: 测试模式可视化数据
        """
        log.info("生成测试模式可视化数据")

        visualization_data = []

        for canvas_cm in canvas_results_cm:
            # 为测试模式，直接使用cm作为px（1:1比例）
            canvas_width_px = int(round(canvas_cm.width_cm))
            canvas_height_px = int(round(canvas_cm.height_cm))

            # 生成颜色块数据
            color_blocks = []
            for i, placement_cm in enumerate(canvas_cm.placements):
                # 生成不同颜色
                colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
                         '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9']
                color = colors[i % len(colors)]

                block = {
                    'x': int(round(placement_cm.x_cm)),
                    'y': int(round(placement_cm.y_cm)),
                    'width': int(round(placement_cm.width_cm)),
                    'height': int(round(placement_cm.height_cm)),
                    'color': color,
                    'image_id': placement_cm.image_id,
                    'rotated': placement_cm.rotated
                }
                color_blocks.append(block)

            canvas_viz = {
                'canvas_index': canvas_cm.canvas_index,
                'width_px': canvas_width_px,
                'height_px': canvas_height_px,
                'color_blocks': color_blocks,
                'utilization_rate': canvas_cm.utilization_rate,
                'stats': {
                    'total_images': len(canvas_cm.placements),
                    'rotated_images': sum(1 for p in canvas_cm.placements if p.rotated),
                    'area_used_cm2': canvas_cm.area_used_cm2,
                    'canvas_area_cm2': canvas_cm.canvas_area_cm2
                }
            }
            visualization_data.append(canvas_viz)

        log.info(f"测试模式可视化数据生成完成，{len(visualization_data)}个画布")
        return visualization_data

    def get_optimization_stats(self, canvas_results_cm: List[CanvasResultCM]) -> Dict[str, Any]:
        """
        获取优化统计信息

        Args:
            canvas_results_cm: CM单位画布结果

        Returns:
            Dict: 统计信息
        """
        if not canvas_results_cm:
            return {
                'total_canvases': 0,
                'total_images': 0,
                'overall_utilization': 0.0,
                'total_area_used_cm2': 0.0,
                'total_canvas_area_cm2': 0.0,
                'average_utilization': 0.0,
                'best_canvas_utilization': 0.0,
                'worst_canvas_utilization': 0.0
            }

        total_area_used = sum(c.area_used_cm2 for c in canvas_results_cm)
        total_canvas_area = sum(c.canvas_area_cm2 for c in canvas_results_cm)
        total_images = sum(len(c.placements) for c in canvas_results_cm)

        utilizations = [c.utilization_rate for c in canvas_results_cm]

        stats = {
            'total_canvases': len(canvas_results_cm),
            'total_images': total_images,
            'overall_utilization': (total_area_used / total_canvas_area * 100) if total_canvas_area > 0 else 0.0,
            'total_area_used_cm2': total_area_used,
            'total_canvas_area_cm2': total_canvas_area,
            'average_utilization': sum(utilizations) / len(utilizations),
            'best_canvas_utilization': max(utilizations),
            'worst_canvas_utilization': min(utilizations),
            'canvas_details': [
                {
                    'canvas_index': c.canvas_index,
                    'utilization': c.utilization_rate,
                    'images_count': len(c.placements),
                    'height_cm': c.height_cm
                }
                for c in canvas_results_cm
            ]
        }

        return stats


def main():
    """测试函数"""
    # 测试数据
    test_images = [
        {'width_cm': 190, 'height_cm': 82, 'name': 'image_1'},
        {'width_cm': 163, 'height_cm': 123, 'name': 'image_2'},
        {'width_cm': 137, 'height_cm': 51, 'name': 'image_3'},
        {'width_cm': 123, 'height_cm': 52, 'name': 'image_4'},
        {'width_cm': 92, 'height_cm': 52, 'name': 'image_5'}
    ]

    # 创建核心实例
    core = RectPackCMCore(canvas_width_cm=163.0, max_height_cm=5000.0)

    # 执行排列
    canvas_results = core.arrange_images_cm(test_images)

    # 获取统计信息
    stats = core.get_optimization_stats(canvas_results)

    print("RectPack CM核心测试结果:")
    print(f"画布数量: {stats['total_canvases']}")
    print(f"图片数量: {stats['total_images']}")
    print(f"整体利用率: {stats['overall_utilization']:.2f}%")

    # 转换为PX用于PS
    px_results = core.convert_to_px_for_ps(canvas_results, ppi=72)
    print(f"PX转换完成，{len(px_results)}个画布")

    # 生成测试模式可视化
    viz_data = core.generate_test_mode_visualization(canvas_results)
    print(f"测试模式可视化数据生成完成，{len(viz_data)}个画布")


if __name__ == "__main__":
    main()
