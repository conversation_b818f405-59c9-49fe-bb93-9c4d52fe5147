#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能分组优化器测试脚本
验证优化算法是否能够达到93%以上的横向利用率
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.intelligent_grouping_optimizer import IntelligentGroupingOptimizer

def test_163cm_canvas_with_137x51_images():
    """测试163cm画布上137x51cm图片的优化效果"""
    print("=" * 60)
    print("测试场景：163cm画布 + 137x51cm图片")
    print("=" * 60)
    
    # 创建测试数据：模拟用户提供的数据
    pattern_items = []
    
    # 创建45个137x51cm的图片（模拟用户数据中的大量相同尺寸图片）
    for i in range(45):
        pattern_items.append({
            'pattern_name': f'图案_{i+1}',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'path': f'test_image_{i+1}.jpg',
            'quantity': 1
        })
    
    # 添加一些其他尺寸的图片
    other_sizes = [
        (123.0, 52.0, 3),  # 3张123x52cm的图片
        (102.0, 52.0, 2),  # 2张102x52cm的图片
        (92.0, 57.0, 1),   # 1张92x57cm的图片
    ]
    
    for width, height, count in other_sizes:
        for i in range(count):
            pattern_items.append({
                'pattern_name': f'其他图案_{width}x{height}_{i+1}',
                'width_cm': width,
                'height_cm': height,
                'path': f'test_other_{width}x{height}_{i+1}.jpg',
                'quantity': 1
            })
    
    print(f"测试数据：{len(pattern_items)} 张图片")
    print(f"主要尺寸：137x51cm ({sum(1 for p in pattern_items if p['width_cm'] == 137.0)} 张)")
    
    # 创建智能分组优化器
    optimizer = IntelligentGroupingOptimizer(
        canvas_width_cm=163.0,
        target_utilization=0.93,
        log_signal=None  # 使用print输出
    )
    
    # 执行优化
    optimized_items = optimizer.optimize_image_grouping(pattern_items)
    
    # 分析优化结果
    print("\n" + "=" * 60)
    print("优化结果分析")
    print("=" * 60)
    
    # 统计优化的图片
    optimized_count = sum(1 for item in optimized_items if item.get('intelligent_rotation_suggested', False))
    print(f"建议旋转的图片数量: {optimized_count}/{len(optimized_items)}")
    
    # 分析137x51cm图片的优化效果
    main_size_items = [item for item in optimized_items if item['width_cm'] == 137.0 and item['height_cm'] == 51.0]
    main_size_rotated = sum(1 for item in main_size_items if item.get('intelligent_rotation_suggested', False))
    
    print(f"137x51cm图片优化情况: {main_size_rotated}/{len(main_size_items)} 建议旋转")
    
    if main_size_rotated > 0:
        # 计算理论利用率
        rotated_width = 51.0  # 旋转后的宽度
        fit_count = int(163.0 / rotated_width)
        total_width = fit_count * rotated_width
        utilization = total_width / 163.0
        
        print(f"理论横向利用率计算:")
        print(f"  - 旋转后尺寸: 51x137cm")
        print(f"  - 横向放置数量: {fit_count} 张")
        print(f"  - 总宽度: {total_width}cm")
        print(f"  - 利用率: {utilization:.1%}")
        
        if utilization >= 0.93:
            print("✅ 达到目标利用率93%！")
        else:
            print("❌ 未达到目标利用率93%")
    
    # 显示优化建议的详细信息
    print(f"\n优化建议详情:")
    for item in optimized_items[:10]:  # 只显示前10个
        if item.get('intelligent_rotation_suggested'):
            reason = item.get('optimization_reason', '未知原因')
            print(f"  - {item['pattern_name']}: {item['width_cm']}x{item['height_cm']}cm -> 建议旋转 ({reason})")
    
    return optimized_items

def test_different_canvas_sizes():
    """测试不同画布尺寸的优化效果"""
    print("\n" + "=" * 60)
    print("测试不同画布尺寸的优化效果")
    print("=" * 60)
    
    # 测试数据：137x51cm的图片
    pattern_items = []
    for i in range(20):
        pattern_items.append({
            'pattern_name': f'测试图片_{i+1}',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'path': f'test_{i+1}.jpg',
            'quantity': 1
        })
    
    # 测试不同的画布宽度
    canvas_widths = [150, 163, 180, 200]
    
    for canvas_width in canvas_widths:
        print(f"\n画布宽度: {canvas_width}cm")
        
        optimizer = IntelligentGroupingOptimizer(
            canvas_width_cm=canvas_width,
            target_utilization=0.93,
            log_signal=None
        )
        
        # 计算原始方向的利用率
        original_fit = int(canvas_width / 137.0)
        original_utilization = (original_fit * 137.0) / canvas_width
        
        # 计算旋转后的利用率
        rotated_fit = int(canvas_width / 51.0)
        rotated_utilization = (rotated_fit * 51.0) / canvas_width
        
        print(f"  原始方向 (137x51): {original_fit}张/行, 利用率 {original_utilization:.1%}")
        print(f"  旋转方向 (51x137): {rotated_fit}张/行, 利用率 {rotated_utilization:.1%}")
        
        # 执行优化
        optimized_items = optimizer.optimize_image_grouping(pattern_items)
        optimized_count = sum(1 for item in optimized_items if item.get('intelligent_rotation_suggested', False))
        
        if optimized_count > 0:
            print(f"  ✅ 建议旋转: {optimized_count}/{len(optimized_items)} 张")
        else:
            print(f"  ➡️ 保持原方向: {len(optimized_items)} 张")

def test_mixed_sizes():
    """测试混合尺寸图片的优化效果"""
    print("\n" + "=" * 60)
    print("测试混合尺寸图片的优化效果")
    print("=" * 60)
    
    # 创建混合尺寸的测试数据
    pattern_items = []
    
    # 不同尺寸的图片组
    size_groups = [
        (137.0, 51.0, 15),  # 15张137x51cm
        (120.0, 45.0, 8),   # 8张120x45cm
        (100.0, 60.0, 6),   # 6张100x60cm
        (80.0, 40.0, 10),   # 10张80x40cm
        (150.0, 30.0, 5),   # 5张150x30cm
    ]
    
    for width, height, count in size_groups:
        for i in range(count):
            pattern_items.append({
                'pattern_name': f'图片_{width}x{height}_{i+1}',
                'width_cm': width,
                'height_cm': height,
                'path': f'test_{width}x{height}_{i+1}.jpg',
                'quantity': 1
            })
    
    print(f"测试数据：{len(pattern_items)} 张混合尺寸图片")
    
    # 163cm画布优化
    optimizer = IntelligentGroupingOptimizer(
        canvas_width_cm=163.0,
        target_utilization=0.93,
        log_signal=None
    )
    
    optimized_items = optimizer.optimize_image_grouping(pattern_items)
    
    # 按尺寸分组统计优化结果
    print(f"\n各尺寸组优化结果:")
    for width, height, count in size_groups:
        group_items = [item for item in optimized_items 
                      if item['width_cm'] == width and item['height_cm'] == height]
        rotated_count = sum(1 for item in group_items if item.get('intelligent_rotation_suggested', False))
        
        # 计算理论利用率
        original_fit = int(163.0 / width)
        original_util = (original_fit * width) / 163.0
        rotated_fit = int(163.0 / height)
        rotated_util = (rotated_fit * height) / 163.0
        
        print(f"  {width}x{height}cm ({count}张):")
        print(f"    原始: {original_fit}张/行, {original_util:.1%} | 旋转: {rotated_fit}张/行, {rotated_util:.1%}")
        print(f"    建议旋转: {rotated_count}/{count} 张")

if __name__ == "__main__":
    print("智能分组优化器测试")
    print("目标：通过智能旋转和分组策略，达到93%以上的横向利用率")
    
    # 测试1：主要场景 - 163cm画布 + 137x51cm图片
    test_163cm_canvas_with_137x51_images()
    
    # 测试2：不同画布尺寸
    test_different_canvas_sizes()
    
    # 测试3：混合尺寸
    test_mixed_sizes()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
