# RectPack算法参数详细说明文档

## 概述

RectPack算法是本项目的核心图片排列算法，用于在指定容器（画布）中高效排列图片。本文档详细说明了所有可配置参数的含义、作用机制、推荐设置和最佳实践。

## 参数分类

### 1. 核心算法参数

#### 1.1 rotation_enabled (启用旋转)
- **类型**: Boolean
- **默认值**: True
- **作用**: 控制算法是否可以旋转图片以获得更好的排列效果
- **影响**: 
  - True: 显著提高空间利用率，特别是对202cm宽画布的大尺寸图片
  - False: 保持图片原始方向，可能降低利用率但保持视觉一致性
- **推荐设置**: True（经过测试验证，对202cm宽画布特别重要）
- **与项目关联**: 对A类和B类图片的排列效果影响最大

#### 1.2 sort_strategy (排序策略)
- **类型**: Integer (0-5)
- **默认值**: 0 (AREA - 面积排序)
- **可选值**:
  - 0: AREA - 按面积排序（推荐）
  - 1: PERIMETER - 按周长排序
  - 2: DIFFERENCE - 按宽高差排序
  - 3: SHORT_SIDE - 按短边排序
  - 4: LONG_SIDE - 按长边排序
  - 5: RATIO - 按宽高比排序
- **作用**: 决定图片在装箱前的排序方式
- **影响**: 
  - AREA排序通常产生最高的空间利用率
  - 不同排序策略适合不同的图片尺寸分布
- **推荐设置**: 0 (AREA) - 经过测试验证的最优选择
- **与项目关联**: 影响A/B/C类图片的整体排列顺序和效果

#### 1.3 pack_algorithm (装箱算法)
- **类型**: Integer (0-2)
- **默认值**: 0 (BSSF - Best Short Side Fit)
- **可选值**:
  - 0: BSSF - Best Short Side Fit（推荐）
  - 1: BFF - Best Fit First
  - 2: BBF - Best Bin First
- **作用**: 选择具体的矩形装箱算法
- **影响**:
  - BSSF: 优先选择短边最适合的位置，通常产生最佳空间利用率
  - BFF: 优先选择最适合的位置，平衡利用率和排列紧密度
  - BBF: 优先选择最适合的容器，适合多容器场景
- **推荐设置**: 0 (BSSF) - 经过测试验证的最佳空间利用率算法
- **与项目关联**: 直接影响最终的画布利用率和图片排列紧密度

### 2. 优化参数

#### 2.1 enable_optimization (启用优化)
- **类型**: Boolean
- **默认值**: True
- **作用**: 控制是否启用算法优化功能
- **影响**: 启用后会进行多次迭代优化，提高排列质量但增加计算时间
- **推荐设置**: True
- **与项目关联**: 对多容器场景的整体利用率优化特别重要

#### 2.2 optimization_iterations (优化迭代次数)
- **类型**: Integer (1-100)
- **默认值**: 5
- **作用**: 控制优化算法的迭代次数
- **影响**:
  - 更多迭代: 可能获得更好的排列效果，但显著增加计算时间
  - 较少迭代: 快速完成，但可能错过更优解
- **推荐设置**: 5 - 用户偏好的平衡点，兼顾效果和性能
- **性能考虑**: 每增加1次迭代约增加20%的计算时间
- **与项目关联**: 特别影响C类图片的Tetris算法优化效果

#### 2.3 min_utilization_threshold (最小利用率阈值)
- **类型**: Float (50.0-100.0)
- **默认值**: 80.0
- **单位**: 百分比
- **作用**: 设置可接受的最低空间利用率
- **影响**: 
  - 高阈值: 追求更高利用率，可能增加计算时间
  - 低阈值: 更快完成，但可能接受较低的利用率
- **推荐设置**: 80.0% - 经过测试验证的合理目标
- **与项目关联**: 影响多画布场景下的画布数量和整体效率

#### 2.4 rotation_penalty (旋转惩罚)
- **类型**: Float (0.0-1.0)
- **默认值**: 0.02
- **作用**: 对旋转图片施加的惩罚权重
- **影响**:
  - 低惩罚(0.0-0.1): 鼓励旋转，提高利用率
  - 高惩罚(0.5-1.0): 减少旋转，保持视觉一致性
- **推荐设置**: 0.02 - 低旋转惩罚，鼓励有效旋转优化
- **与项目关联**: 平衡图片排列的利用率和视觉效果

#### 2.5 aspect_ratio_preference (宽高比偏好)
- **类型**: Float (0.1-10.0)
- **默认值**: 1.2
- **作用**: 控制对特定宽高比的偏好
- **影响**:
  - 1.0: 无偏好
  - >1.0: 偏好横向布局
  - <1.0: 偏好纵向布局
- **推荐设置**: 1.2 - 轻微偏好横向布局，符合202cm宽画布特性
- **与项目关联**: 适应项目的画布尺寸特点（宽度大于高度）

### 3. 高级参数

#### 3.1 bin_selection_strategy (容器选择策略)
- **类型**: Integer (0-2)
- **默认值**: 0
- **作用**: 在多容器场景下选择容器的策略
- **推荐设置**: 0 - 简化策略，确保稳定性
- **与项目关联**: 影响多画布场景下的容器分配

#### 3.2 split_heuristic (分割启发式)
- **类型**: Integer (0-2)
- **默认值**: 0
- **作用**: 控制空间分割的启发式方法
- **推荐设置**: 0 - 简化策略
- **与项目关联**: 影响空间分割的精确度

#### 3.3 free_rect_choice (自由矩形选择)
- **类型**: Integer (0-2)
- **默认值**: 0
- **作用**: 控制自由矩形的选择策略
- **推荐设置**: 0 - 简化策略
- **与项目关联**: 影响空间利用的细节优化

### 4. 性能参数

#### 4.1 max_processing_time (最大处理时间)
- **类型**: Integer (60-3600)
- **默认值**: 300
- **单位**: 秒
- **作用**: 设置算法的最大执行时间
- **影响**: 防止算法在复杂场景下无限运行
- **推荐设置**: 300秒 (5分钟) - 适合大多数场景
- **与项目关联**: 确保用户不会等待过久

#### 4.2 batch_size (批处理大小)
- **类型**: Integer (10-1000)
- **默认值**: 100
- **作用**: 控制每批处理的图片数量
- **影响**: 
  - 大批次: 更好的优化效果，但占用更多内存
  - 小批次: 内存友好，但可能影响优化质量
- **推荐设置**: 100 - 适合多容器处理
- **与项目关联**: 平衡内存使用和处理效率

#### 4.3 memory_limit_mb (内存限制)
- **类型**: Integer (128-4096)
- **默认值**: 512
- **单位**: MB
- **作用**: 限制算法使用的最大内存
- **影响**: 防止内存溢出，确保系统稳定性
- **推荐设置**: 512MB - 适合大多数系统配置
- **与项目关联**: 确保与Photoshop等其他应用的兼容性

#### 4.4 enable_parallel (启用并行处理)
- **类型**: Boolean
- **默认值**: False
- **作用**: 控制是否使用多线程加速
- **影响**: 可能提高处理速度，但可能与Photoshop冲突
- **推荐设置**: False - 避免与Photoshop的冲突
- **与项目关联**: 确保与Photoshop自动化的稳定性

### 5. 调试参数

#### 5.1 debug_mode (调试模式)
- **类型**: Boolean
- **默认值**: False
- **作用**: 启用详细的调试信息输出
- **推荐设置**: False (生产环境)
- **与项目关联**: 用于问题诊断和算法优化

#### 5.2 log_level (日志级别)
- **类型**: Integer (0-3)
- **默认值**: 1
- **可选值**:
  - 0: 无日志
  - 1: 基础日志（推荐）
  - 2: 详细日志
  - 3: 调试日志
- **推荐设置**: 1 - 基础日志级别
- **与项目关联**: 平衡信息输出和性能

## 容器约束配置

### 容器尺寸设置
- **画布宽度**: 200cm + 水平拓展(默认2cm) = 202cm
- **最大高度**: 5000cm
- **图片间距**: 默认2px (0.1cm)

### 多容器支持
当单个容器无法容纳所有图片时，算法会自动创建多个容器，每个容器都遵循相同的尺寸约束。

## 最佳实践建议

### 1. 基础配置
对于大多数场景，推荐使用以下基础配置：
```
rotation_enabled: True
sort_strategy: 0 (AREA)
pack_algorithm: 0 (BSSF)
optimization_iterations: 5
min_utilization_threshold: 80.0
```

### 2. 性能优化
对于大量图片或复杂场景：
```
batch_size: 50-100
memory_limit_mb: 512-1024
max_processing_time: 300-600
enable_parallel: False
```

### 3. 质量优化
追求最高质量时：
```
optimization_iterations: 10
min_utilization_threshold: 85.0
rotation_penalty: 0.01
```

### 4. 快速处理
需要快速完成时：
```
optimization_iterations: 3
enable_optimization: False
debug_mode: False
```

## 参数组合效果分析

### 高利用率组合
- rotation_enabled: True
- sort_strategy: 0 (AREA)
- optimization_iterations: 5-10
- min_utilization_threshold: 85.0+

### 快速处理组合
- optimization_iterations: 1-3
- enable_optimization: False
- batch_size: 200+

### 视觉一致性组合
- rotation_penalty: 0.1-0.5
- aspect_ratio_preference: 1.0
- sort_strategy: 3 (SHORT_SIDE)

## 与A/B/C类图片分类系统的关联

### A类图片
- 主要受 rotation_enabled 和 pack_algorithm 影响
- 通常直接放置或旋转90度

### B类图片
- 受 sort_strategy 和 optimization_iterations 影响较大
- 需要考虑除数关系的排列优化

### C类图片
- 最受益于完整的优化参数配置
- Tetris算法的效果直接依赖于这些参数

## 故障排除

### 常见问题
1. **利用率低**: 检查 rotation_enabled 和 optimization_iterations
2. **处理时间长**: 降低 optimization_iterations 或启用 batch_size
3. **内存不足**: 降低 memory_limit_mb 和 batch_size
4. **结果不一致**: 检查 enable_parallel 设置

### 参数验证
使用 `utils/rectpack_param_validator.py` 工具验证参数传递的正确性。
