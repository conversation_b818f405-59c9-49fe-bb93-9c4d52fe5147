#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强RectPack算法演示
展示如何使用新的约束条件和优化策略解决实际布局问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.rectpack_arranger import RectPackArranger
from core.rectpack_constraint_manager import RectPackConstraintManager

def demo_user_scenario():
    """演示用户的实际场景：163cm画布 + 137x51cm图片"""
    print("=" * 70)
    print("演示用户实际场景：163cm画布 + 137x51cm图片")
    print("=" * 70)
    
    # 场景参数
    canvas_width_cm = 163.0
    canvas_width_px = int(canvas_width_cm * 72 / 2.54)  # 转换为像素
    max_height_px = int(200.0 * 72 / 2.54)  # 最大高度200cm
    
    print(f"画布配置:")
    print(f"  - 宽度: {canvas_width_cm}cm ({canvas_width_px}px)")
    print(f"  - 最大高度: 200cm ({max_height_px}px)")
    print(f"  - PPI: 72")
    
    # 创建增强的RectPack排列器
    arranger = RectPackArranger(
        container_width=canvas_width_px,
        image_spacing=0,
        max_height=max_height_px
    )
    
    print(f"\n✅ 增强RectPack排列器已创建")
    print(f"  - 集成智能分组优化器")
    print(f"  - 集成约束管理器")
    print(f"  - 支持边长微调策略")
    
    # 模拟用户的图片数据
    test_images = []
    
    # 添加大量137x51cm的图片（用户的主要场景）
    for i in range(15):
        width_px = int(137.0 * 72 / 2.54)
        height_px = int(51.0 * 72 / 2.54)
        
        image_data = {
            'name': f'主要图片_{i+1}',
            'path': f'test_main_{i+1}.jpg',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'original_width_px': width_px,
            'original_height_px': height_px,
            'unique_id': f'main_img_{i+1}'
        }
        
        test_images.append((width_px, height_px, image_data))
    
    # 添加一些其他尺寸的图片
    other_sizes = [
        (120.0, 45.0, 3),  # 3张120x45cm
        (100.0, 60.0, 2),  # 2张100x60cm
    ]
    
    for width_cm, height_cm, count in other_sizes:
        for i in range(count):
            width_px = int(width_cm * 72 / 2.54)
            height_px = int(height_cm * 72 / 2.54)
            
            image_data = {
                'name': f'其他图片_{width_cm}x{height_cm}_{i+1}',
                'path': f'test_other_{width_cm}x{height_cm}_{i+1}.jpg',
                'width_cm': width_cm,
                'height_cm': height_cm,
                'original_width_px': width_px,
                'original_height_px': height_px,
                'unique_id': f'other_img_{width_cm}x{height_cm}_{i+1}'
            }
            
            test_images.append((width_px, height_px, image_data))
    
    print(f"\n测试图片数据:")
    print(f"  - 总图片数: {len(test_images)}")
    print(f"  - 137x51cm图片: 15张")
    print(f"  - 其他尺寸图片: {len(test_images) - 15}张")
    
    # 执行排列
    print(f"\n开始执行增强排列算法...")
    
    placed_count = 0
    rotated_count = 0
    adjusted_count = 0
    failed_count = 0
    
    for i, (width_px, height_px, image_data) in enumerate(test_images):
        # 尝试放置图片
        x, y, success = arranger.place_image(width_px, height_px, image_data)
        
        if success:
            placed_count += 1
            
            # 检查是否旋转
            if image_data.get('need_rotation', False):
                rotated_count += 1
                
            # 检查是否进行了边长调整
            if image_data.get('edge_adjusted', False):
                adjusted_count += 1
                
            # 显示前几个图片的放置结果
            if i < 5:
                rotation_info = " (旋转90°)" if image_data.get('need_rotation', False) else ""
                adjustment_info = " (边长调整)" if image_data.get('edge_adjusted', False) else ""
                print(f"  图片{i+1}: 放置在 ({x},{y}){rotation_info}{adjustment_info}")
        else:
            failed_count += 1
            if failed_count == 1:
                print(f"  图片{i+1}: 放置失败，画布已满")
    
    # 获取布局信息
    layout_info = arranger.get_layout_info()
    
    print(f"\n排列结果:")
    print(f"  - 成功放置: {placed_count}/{len(test_images)} 张")
    print(f"  - 旋转图片: {rotated_count} 张")
    print(f"  - 边长调整: {adjusted_count} 张")
    print(f"  - 失败图片: {failed_count} 张")
    
    print(f"\n布局统计:")
    print(f"  - 画布尺寸: {layout_info['container_width']}x{layout_info['container_height']}px")
    print(f"  - 空间利用率: {layout_info['utilization_percent']:.2f}%")
    print(f"  - 已用面积: {layout_info['used_area']:,}px²")
    print(f"  - 总面积: {layout_info['total_area']:,}px²")
    
    # 获取约束统计
    constraint_stats = arranger.get_constraint_statistics()
    if constraint_stats.get('available', False):
        print(f"\n约束统计:")
        print(f"  - 约束检查次数: {constraint_stats['constraint_checks']}")
        print(f"  - 约束违反次数: {constraint_stats['constraint_violations']}")
        print(f"  - 违反率: {constraint_stats['violation_rate']:.1%}")
        print(f"  - 调整应用次数: {constraint_stats['adjustments_applied']}")
    
    # 分析主要尺寸图片的优化效果
    main_size_placed = 0
    main_size_rotated = 0
    
    for img in arranger.placed_images:
        if abs(img.get('width_cm', 0) - 137.0) < 0.1 and abs(img.get('height_cm', 0) - 51.0) < 0.1:
            main_size_placed += 1
            if img.get('rotated', False):
                main_size_rotated += 1
    
    print(f"\n137x51cm图片优化分析:")
    print(f"  - 成功放置: {main_size_placed}/15 张")
    print(f"  - 智能旋转: {main_size_rotated}/{main_size_placed} 张")
    
    if main_size_rotated > 0:
        # 计算理论利用率
        rotated_width = 51.0  # 旋转后的宽度
        fit_count = int(canvas_width_cm / rotated_width)
        total_width = fit_count * rotated_width
        utilization = total_width / canvas_width_cm
        
        print(f"  - 理论横向利用率: {utilization:.1%}")
        print(f"  - 横向放置数量: {fit_count} 张/行")
        print(f"  - 宽度间隙: {canvas_width_cm - total_width:.1f}cm")
        
        if utilization >= 0.93:
            print(f"  ✅ 达到目标利用率93%！")
        else:
            print(f"  ⚠️ 未达到目标利用率93%")

def demo_constraint_scenarios():
    """演示不同约束场景的处理"""
    print("\n" + "=" * 70)
    print("演示不同约束场景的处理")
    print("=" * 70)
    
    # 创建约束管理器
    manager = RectPackConstraintManager(
        canvas_width_cm=163.0,
        max_width_gap_cm=10.0,
        max_edge_reduction_cm=1.0
    )
    
    scenarios = [
        {
            'name': '理想场景：3张51x137cm图片（旋转后）',
            'images': [
                {'width_cm': 51.0, 'height_cm': 137.0, 'unique_id': 'ideal_1'},
                {'width_cm': 51.0, 'height_cm': 137.0, 'unique_id': 'ideal_2'},
                {'width_cm': 51.0, 'height_cm': 137.0, 'unique_id': 'ideal_3'},
            ],
            'description': '智能分组优化的理想结果'
        },
        {
            'name': '需要微调：3张52x137cm图片',
            'images': [
                {'width_cm': 52.0, 'height_cm': 137.0, 'unique_id': 'adjust_1'},
                {'width_cm': 52.0, 'height_cm': 137.0, 'unique_id': 'adjust_2'},
                {'width_cm': 52.0, 'height_cm': 137.0, 'unique_id': 'adjust_3'},
            ],
            'description': '需要边长微调的场景'
        },
        {
            'name': '超出能力：3张60x137cm图片',
            'images': [
                {'width_cm': 60.0, 'height_cm': 137.0, 'unique_id': 'exceed_1'},
                {'width_cm': 60.0, 'height_cm': 137.0, 'unique_id': 'exceed_2'},
                {'width_cm': 60.0, 'height_cm': 137.0, 'unique_id': 'exceed_3'},
            ],
            'description': '超出微调能力的场景'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n场景: {scenario['name']}")
        print(f"描述: {scenario['description']}")
        
        # 计算当前总宽度
        current_total = sum(img['width_cm'] for img in scenario['images'])
        target_total = 163.0 - 10.0  # 目标总宽度
        
        print(f"  当前总宽度: {current_total}cm")
        print(f"  目标总宽度: {target_total}cm")
        print(f"  需要调整: {current_total - target_total:.1f}cm")
        
        # 检查约束
        placed_images_px = []
        for img in scenario['images']:
            width_px = int(img['width_cm'] * 72 / 2.54)
            height_px = int(img['height_cm'] * 72 / 2.54)
            placed_images_px.append({'width': width_px, 'height': height_px})
        
        violation = manager.check_width_gap_constraint(placed_images_px)
        
        if violation:
            print(f"  ❌ 约束违反: {violation.message}")
            
            # 尝试边长微调
            print(f"  🔧 尝试边长微调...")
            adjustments = manager.calculate_optimal_adjustments(scenario['images'], target_total)
            
            total_reduction = sum(adj.get_total_reduction() for adj in adjustments)
            optimized_total = sum(adj.adjusted_width for adj in adjustments)
            
            print(f"    总缩减量: {total_reduction:.1f}cm")
            print(f"    优化后总宽度: {optimized_total:.1f}cm")
            
            if optimized_total <= target_total:
                print(f"    ✅ 微调成功，满足约束")
            else:
                remaining_gap = optimized_total - target_total
                print(f"    ⚠️ 微调不足，剩余缺口: {remaining_gap:.1f}cm")
        else:
            print(f"  ✅ 约束满足，无需调整")

if __name__ == "__main__":
    print("增强RectPack算法演示")
    print("展示新的约束条件和优化策略")
    
    # 演示1：用户实际场景
    demo_user_scenario()
    
    # 演示2：不同约束场景
    demo_constraint_scenarios()
    
    print("\n" + "=" * 70)
    print("演示完成！")
    print("=" * 70)
    print("\n总结:")
    print("✅ 智能分组优化：自动识别相同尺寸图片，建议最优旋转策略")
    print("✅ 宽度间隙控制：确保布局满足10cm间隙限制")
    print("✅ 边长微调策略：在约束范围内进行精细优化")
    print("✅ 约束统计监控：提供详细的优化效果分析")
    print("✅ 无缝系统集成：保持API兼容性，自动启用新功能")
