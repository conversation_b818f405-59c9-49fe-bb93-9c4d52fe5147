#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法高级参数优化器
专门用于达到95%以上的利用率
"""

import logging
import time
from typing import List, Tuple, Dict, Any
from dataclasses import dataclass
import itertools
import copy

log = logging.getLogger(__name__)

@dataclass
class AdvancedOptimizationResult:
    """高级优化结果"""
    utilization_rate: float
    parameters: Dict[str, Any]
    canvas_count: int
    total_area_used: float
    total_canvas_area: float
    processing_time: float
    success: bool
    details: str
    canvas_details: List[Dict[str, Any]]

class RectPackAdvancedOptimizer:
    """RectPack算法高级参数优化器"""
    
    def __init__(self, canvas_width_cm: float = 163.0, max_height_cm: float = 5000.0):
        """
        初始化高级优化器
        
        Args:
            canvas_width_cm: 画布宽度（cm）
            max_height_cm: 最大高度（cm）
        """
        self.canvas_width_cm = canvas_width_cm
        self.max_height_cm = max_height_cm
        
        log.info(f"RectPack高级参数优化器初始化: {canvas_width_cm}cm x {max_height_cm}cm")
    
    def optimize_for_95_percent(self, image_data: List[Tuple[float, float, int]]) -> AdvancedOptimizationResult:
        """
        专门优化以达到95%以上的利用率
        
        Args:
            image_data: 图片数据列表 [(宽度cm, 高度cm, 数量), ...]
            
        Returns:
            AdvancedOptimizationResult: 高级优化结果
        """
        log.info("开始高级优化，目标利用率: 95%+")
        start_time = time.time()
        
        # 准备图片数据
        images = self._prepare_image_data(image_data)
        log.info(f"准备了 {len(images)} 张图片进行高级优化测试")
        
        # 多阶段优化策略
        best_result = None
        
        # 第一阶段：基础参数优化
        log.info("第一阶段：基础参数优化")
        stage1_result = self._stage1_basic_optimization(images)
        if stage1_result and stage1_result.utilization_rate >= 95.0:
            best_result = stage1_result
            log.info(f"第一阶段达到目标: {stage1_result.utilization_rate:.2f}%")
        else:
            best_result = stage1_result
        
        # 第二阶段：精细调整优化
        if not best_result or best_result.utilization_rate < 95.0:
            log.info("第二阶段：精细调整优化")
            stage2_result = self._stage2_fine_tuning(images, best_result)
            if stage2_result and stage2_result.utilization_rate > (best_result.utilization_rate if best_result else 0):
                best_result = stage2_result
        
        # 第三阶段：智能分组优化
        if not best_result or best_result.utilization_rate < 95.0:
            log.info("第三阶段：智能分组优化")
            stage3_result = self._stage3_smart_grouping(images, best_result)
            if stage3_result and stage3_result.utilization_rate > (best_result.utilization_rate if best_result else 0):
                best_result = stage3_result
        
        processing_time = time.time() - start_time
        
        if best_result:
            best_result.processing_time = processing_time
            log.info(f"高级优化完成: 最佳利用率 {best_result.utilization_rate:.2f}%，耗时 {processing_time:.2f}秒")
        else:
            log.warning("高级优化失败")
            best_result = AdvancedOptimizationResult(
                utilization_rate=0.0,
                parameters={},
                canvas_count=0,
                total_area_used=0.0,
                total_canvas_area=0.0,
                processing_time=processing_time,
                success=False,
                details="所有优化阶段都失败",
                canvas_details=[]
            )
        
        return best_result
    
    def _prepare_image_data(self, image_data: List[Tuple[float, float, int]]) -> List[Dict[str, Any]]:
        """准备图片数据"""
        images = []
        image_id = 1
        
        for width_cm, height_cm, count in image_data:
            for _ in range(count):
                images.append({
                    'id': image_id,
                    'width': width_cm,
                    'height': height_cm,
                    'name': f'image_{image_id}',
                    'area': width_cm * height_cm,
                    'aspect_ratio': width_cm / height_cm if height_cm > 0 else 1.0
                })
                image_id += 1
        
        return images
    
    def _stage1_basic_optimization(self, images: List[Dict[str, Any]]) -> AdvancedOptimizationResult:
        """第一阶段：基础参数优化"""
        # 定义高效参数组合
        parameter_combinations = [
            # 最优组合：短边排序 + BFF算法 + 启用旋转
            {
                'rotation_enabled': True,
                'sort_strategy': 3,  # SHORT_SIDE
                'pack_algorithm': 1,  # BFF
                'spacing_px': 1,
                'edge_shrink_tolerance_cm': 1.0,
                'enable_edge_shrink': True
            },
            # 面积排序 + BSSF算法
            {
                'rotation_enabled': True,
                'sort_strategy': 0,  # AREA
                'pack_algorithm': 0,  # BSSF
                'spacing_px': 1,
                'edge_shrink_tolerance_cm': 1.0,
                'enable_edge_shrink': True
            },
            # 周长排序 + BFF算法
            {
                'rotation_enabled': True,
                'sort_strategy': 1,  # PERIMETER
                'pack_algorithm': 1,  # BFF
                'spacing_px': 1,
                'edge_shrink_tolerance_cm': 1.0,
                'enable_edge_shrink': True
            },
            # 长边排序 + BSSF算法
            {
                'rotation_enabled': True,
                'sort_strategy': 4,  # LONG_SIDE
                'pack_algorithm': 0,  # BSSF
                'spacing_px': 1,
                'edge_shrink_tolerance_cm': 1.0,
                'enable_edge_shrink': True
            }
        ]
        
        best_result = None
        best_utilization = 0.0
        
        for params in parameter_combinations:
            result = self._test_parameter_combination(images, params)
            if result.success and result.utilization_rate > best_utilization:
                best_utilization = result.utilization_rate
                best_result = result
                log.info(f"第一阶段发现更好的参数: {result.utilization_rate:.2f}%")
                
                if result.utilization_rate >= 95.0:
                    break
        
        return best_result
    
    def _stage2_fine_tuning(self, images: List[Dict[str, Any]], base_result: AdvancedOptimizationResult) -> AdvancedOptimizationResult:
        """第二阶段：精细调整优化"""
        if not base_result:
            return None
        
        base_params = base_result.parameters
        best_result = base_result
        
        # 精细调整间距
        for spacing in [0.5, 1, 1.5, 2]:
            params = base_params.copy()
            params['spacing_px'] = spacing
            
            result = self._test_parameter_combination(images, params)
            if result.success and result.utilization_rate > best_result.utilization_rate:
                best_result = result
                log.info(f"第二阶段间距调整: {result.utilization_rate:.2f}%")
        
        # 精细调整边长缩减
        for shrink in [0.3, 0.5, 0.8, 1.0, 1.2]:
            params = base_params.copy()
            params['edge_shrink_tolerance_cm'] = shrink
            
            result = self._test_parameter_combination(images, params)
            if result.success and result.utilization_rate > best_result.utilization_rate:
                best_result = result
                log.info(f"第二阶段边长调整: {result.utilization_rate:.2f}%")
        
        return best_result
    
    def _stage3_smart_grouping(self, images: List[Dict[str, Any]], base_result: AdvancedOptimizationResult) -> AdvancedOptimizationResult:
        """第三阶段：智能分组优化"""
        if not base_result:
            return None
        
        # 按尺寸分组优化
        large_images = [img for img in images if img['area'] > 10000]  # 大于100cm²
        medium_images = [img for img in images if 1000 <= img['area'] <= 10000]
        small_images = [img for img in images if img['area'] < 1000]
        
        log.info(f"图片分组: 大图{len(large_images)}张, 中图{len(medium_images)}张, 小图{len(small_images)}张")
        
        # 尝试不同的分组策略
        grouping_strategies = [
            # 策略1：大图优先
            large_images + medium_images + small_images,
            # 策略2：按面积排序
            sorted(images, key=lambda x: x['area'], reverse=True),
            # 策略3：按宽高比排序
            sorted(images, key=lambda x: x['aspect_ratio'], reverse=True),
            # 策略4：混合排序
            self._mixed_sort(images)
        ]
        
        best_result = base_result
        
        for i, sorted_images in enumerate(grouping_strategies):
            result = self._test_parameter_combination(sorted_images, base_result.parameters)
            if result.success and result.utilization_rate > best_result.utilization_rate:
                best_result = result
                log.info(f"第三阶段策略{i+1}: {result.utilization_rate:.2f}%")
        
        return best_result
    
    def _mixed_sort(self, images: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """混合排序策略"""
        # 先按面积分组
        large = [img for img in images if img['area'] > 10000]
        medium = [img for img in images if 1000 <= img['area'] <= 10000]
        small = [img for img in images if img['area'] < 1000]
        
        # 大图按宽度排序
        large.sort(key=lambda x: x['width'], reverse=True)
        # 中图按面积排序
        medium.sort(key=lambda x: x['area'], reverse=True)
        # 小图按高度排序
        small.sort(key=lambda x: x['height'], reverse=True)
        
        return large + medium + small
    
    def _test_parameter_combination(self, images: List[Dict[str, Any]], 
                                  params: Dict[str, Any]) -> AdvancedOptimizationResult:
        """测试参数组合"""
        try:
            from rectpack import newPacker, SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO
            from rectpack import MaxRectsBssf, MaxRectsBaf, MaxRectsBlsf
            
            sort_algos = [SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO]
            pack_algos = [MaxRectsBssf, MaxRectsBaf, MaxRectsBlsf]
            
            canvas_results = []
            remaining_images = copy.deepcopy(images)
            canvas_index = 0
            
            while remaining_images and canvas_index < 10:  # 最多10个画布
                packer = newPacker(
                    pack_algo=pack_algos[params['pack_algorithm']],
                    sort_algo=sort_algos[params['sort_strategy']],
                    rotation=params['rotation_enabled']
                )
                
                packer.add_bin(self.canvas_width_cm, self.max_height_cm)
                
                # 添加图片
                for img in remaining_images:
                    width = img['width']
                    height = img['height']
                    
                    if params.get('enable_edge_shrink', False):
                        shrink_cm = params['edge_shrink_tolerance_cm']
                        width = max(0.1, width - shrink_cm)
                        height = max(0.1, height - shrink_cm)
                    
                    spacing_cm = params['spacing_px'] * 2.54 / 72
                    width += spacing_cm
                    height += spacing_cm
                    
                    packer.add_rect(width, height, rid=img['id'])
                
                packer.pack()
                
                # 获取结果
                placed_in_current = []
                max_y = 0
                total_area = 0
                
                for rect in packer.rect_list():
                    bin_id, _, y, width, height, rid = rect
                    if bin_id == 0:
                        placed_in_current.append(rid)
                        max_y = max(max_y, y + height)
                        total_area += width * height
                
                if placed_in_current:
                    canvas_area = self.canvas_width_cm * max_y
                    utilization = (total_area / canvas_area * 100) if canvas_area > 0 else 0
                    
                    canvas_results.append({
                        'canvas_index': canvas_index,
                        'placed_count': len(placed_in_current),
                        'height': max_y,
                        'utilization': utilization,
                        'area_used': total_area,
                        'canvas_area': canvas_area
                    })
                    
                    remaining_images = [img for img in remaining_images if img['id'] not in placed_in_current]
                else:
                    break
                
                canvas_index += 1
            
            if canvas_results:
                total_area_used = sum(c['area_used'] for c in canvas_results)
                total_canvas_area = sum(c['canvas_area'] for c in canvas_results)
                overall_utilization = (total_area_used / total_canvas_area * 100) if total_canvas_area > 0 else 0
                
                return AdvancedOptimizationResult(
                    utilization_rate=overall_utilization,
                    parameters=params,
                    canvas_count=len(canvas_results),
                    total_area_used=total_area_used,
                    total_canvas_area=total_canvas_area,
                    processing_time=0.0,
                    success=True,
                    details=f"成功放置到 {len(canvas_results)} 个画布，剩余 {len(remaining_images)} 张图片",
                    canvas_details=canvas_results
                )
            else:
                return AdvancedOptimizationResult(
                    utilization_rate=0.0,
                    parameters=params,
                    canvas_count=0,
                    total_area_used=0.0,
                    total_canvas_area=0.0,
                    processing_time=0.0,
                    success=False,
                    details="无法放置任何图片",
                    canvas_details=[]
                )
                
        except Exception as e:
            log.error(f"测试参数组合时出错: {str(e)}")
            return AdvancedOptimizationResult(
                utilization_rate=0.0,
                parameters=params,
                canvas_count=0,
                total_area_used=0.0,
                total_canvas_area=0.0,
                processing_time=0.0,
                success=False,
                details=f"测试失败: {str(e)}",
                canvas_details=[]
            )
    
    def save_optimal_parameters_to_config(self, result: AdvancedOptimizationResult) -> bool:
        """将最优参数保存到配置"""
        if not result.success:
            log.error("无法保存失败的优化结果")
            return False
        
        try:
            from utils.config_manager_duckdb import ConfigManagerDuckDB
            config_manager = ConfigManagerDuckDB()
            
            config_updates = {}
            params = result.parameters
            
            # 基础参数
            config_updates['rectpack_rotation_enabled'] = params.get('rotation_enabled', True)
            config_updates['rectpack_sort_strategy'] = params.get('sort_strategy', 0)
            config_updates['rectpack_pack_algorithm'] = params.get('pack_algorithm', 0)
            config_updates['rectpack_spacing_px'] = params.get('spacing_px', 2)
            
            # 优化参数
            config_updates['rectpack_enable_optimization'] = True
            config_updates['rectpack_optimization_iterations'] = 20  # 高级优化使用更多迭代
            config_updates['rectpack_min_utilization_threshold'] = 95.0  # 95%目标
            config_updates['rectpack_rotation_penalty'] = 0.001
            config_updates['rectpack_aspect_ratio_preference'] = 1.0
            
            # 边长缩减参数
            config_updates['rectpack_enable_edge_shrink'] = params.get('enable_edge_shrink', True)
            config_updates['rectpack_edge_shrink_tolerance_cm'] = params.get('edge_shrink_tolerance_cm', 1.0)
            
            # 高级优化参数
            config_updates['rectpack_enable_gap_filling'] = True
            config_updates['rectpack_enable_adaptive_sorting'] = True
            config_updates['rectpack_enable_multi_stage_optimization'] = True
            
            success = config_manager.update(config_updates)
            
            if success:
                log.info(f"成功保存最优RectPack高级参数到配置，利用率: {result.utilization_rate:.2f}%")
                return True
            else:
                log.error("保存最优参数到配置失败")
                return False
                
        except Exception as e:
            log.error(f"保存最优参数时出错: {str(e)}")
            return False
    
    def generate_optimization_report(self, result: AdvancedOptimizationResult) -> str:
        """生成高级优化报告"""
        if not result.success:
            return f"RectPack高级参数优化失败: {result.details}"
        
        report = f"""
RectPack算法高级参数优化报告
============================

优化目标: 95%以上利用率
实际达到: {result.utilization_rate:.2f}%
画布数量: {result.canvas_count}
处理时间: {result.processing_time:.2f}秒

最优参数配置:
-----------
旋转启用: {result.parameters.get('rotation_enabled', 'N/A')}
排序策略: {result.parameters.get('sort_strategy', 'N/A')} (0=面积, 1=周长, 3=短边, 4=长边)
装箱算法: {result.parameters.get('pack_algorithm', 'N/A')} (0=BSSF, 1=BFF, 2=BBF)
图片间距: {result.parameters.get('spacing_px', 'N/A')}px
边长缩减容忍度: {result.parameters.get('edge_shrink_tolerance_cm', 'N/A')}cm

画布利用情况:
-----------
总使用面积: {result.total_area_used:.0f}cm²
总画布面积: {result.total_canvas_area:.0f}cm²
整体利用率: {result.utilization_rate:.2f}%

各画布详情:
-----------"""
        
        for canvas in result.canvas_details:
            report += f"""
画布 {canvas['canvas_index'] + 1}:
  - 放置图片: {canvas['placed_count']}张
  - 画布高度: {canvas['height']:.2f}cm
  - 利用率: {canvas['utilization']:.2f}%
  - 使用面积: {canvas['area_used']:.0f}cm²"""
        
        report += f"\n\n优化详情: {result.details}\n"
        
        return report


def main():
    """主函数 - 用于测试高级优化器"""
    # 测试数据
    IMAGE_DATA = [
        (190, 82, 1), (190, 82, 1), (190, 62, 1), (190, 82, 1), (190, 92, 1),
        (190, 82, 1), (190, 82, 1), (190, 92, 2), (190, 62, 1), (190, 82, 1),
        (190, 92, 1), (190, 92, 1), (190, 92, 1), (190, 92, 1), (185, 52, 1),
        (169.5, 34.5, 1), (163, 123, 1), (163, 123, 1), (155, 30, 1), (153, 62, 1),
        (153, 62, 1), (153, 22, 2), (153, 62, 1), (153, 62, 1), (143, 102, 1),
        (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1),
        (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1),
        (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1),
        (137, 51, 1), (137, 51, 1), (123, 52, 1), (123, 102, 1), (123, 82, 2),
        (123, 32, 1), (123, 62, 1), (123, 62, 1), (123, 52, 1), (123, 62, 1),
        (123, 52, 1), (102, 52, 1), (102, 52, 1), (102, 52, 1), (102, 32, 1),
        (92, 52, 1), (92, 57, 1), (92, 42, 1), (92, 52, 1), (92, 52, 1),
        (82, 52, 1), (82, 32, 1), (82, 32, 1), (82, 52, 1), (77, 47, 1),
        (72, 47, 1), (72, 47, 1), (67, 52, 2), (67, 52, 1), (67, 52, 1),
        (67, 52, 2), (67, 52, 2), (67, 52, 1), (67, 52, 1), (67, 52, 2),
        (67, 52, 1), (67, 52, 2), (67, 52, 2), (67, 52, 2), (67, 52, 2),
        (67, 52, 2), (67, 52, 2), (67, 52, 2), (67, 52, 2), (67, 52, 2),
        (67, 52, 2), (67, 52, 2), (67, 52, 1), (67, 52, 1), (67, 52, 1),
        (67, 52, 1), (67, 52, 2), (67, 52, 1), (67, 52, 2), (67, 52, 2),
        (67, 52, 1), (67, 52, 1), (67, 52, 1), (67, 52, 2), (62, 42, 1),
        (62, 52, 1), (59, 35, 1), (59, 35, 1), (59, 35, 1), (59, 35, 1),
        (52, 52, 2), (52, 52, 2), (52, 52, 2), (245, 30, 1)
    ]
    
    # 创建高级优化器
    optimizer = RectPackAdvancedOptimizer(canvas_width_cm=163.0, max_height_cm=5000.0)
    
    # 执行高级优化
    result = optimizer.optimize_for_95_percent(IMAGE_DATA)
    
    # 生成报告
    report = optimizer.generate_optimization_report(result)
    print(report)
    
    # 保存最优参数
    if result.success:
        optimizer.save_optimal_parameters_to_config(result)


if __name__ == "__main__":
    main()
