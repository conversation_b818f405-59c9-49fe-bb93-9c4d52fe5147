#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试旋转流程脚本
追踪智能旋转建议从智能分组优化器到最终Photoshop输出的完整流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.unified_image_arranger import UnifiedImageArranger

def debug_rotation_flow():
    """调试完整的旋转流程"""
    print("=" * 80)
    print("调试旋转流程：从智能分组优化器到最终输出")
    print("=" * 80)
    
    # 创建测试数据（基于用户报告的137x51cm图片）
    test_images = []
    for i in range(3):  # 只用3张图片便于调试
        test_images.append({
            'pattern_name': f'71915174_{i+1}',
            'path': f'test_image_{i+1}.jpg',  # 假设路径
            'width_cm': 137.0,
            'height_cm': 51.0,
            'quantity': 1,
            'index': i,
            'row_number': 1
        })
    
    print(f"测试数据：3张 137x51cm 图片")
    print(f"画布配置：166cm宽（163cm基础 + 3cm拓展）")
    
    # 创建UnifiedImageArranger（生产模式）
    canvas_width_cm = 166.0
    canvas_width_px = int(canvas_width_cm * 72 / 2.54)
    max_height_px = int(200.0 * 72 / 2.54)
    
    print(f"画布像素：{canvas_width_px}px 宽")
    
    arranger = UnifiedImageArranger()
    arranger.initialize(
        canvas_width_px=canvas_width_px,
        max_height_px=max_height_px,
        image_spacing_px=0,
        ppi=72,
        is_test_mode=False  # 生产模式
    )
    
    # 步骤1：检查智能分组优化器
    print(f"\n" + "=" * 60)
    print("步骤1：智能分组优化器分析")
    print("=" * 60)
    
    if hasattr(arranger, 'grouping_optimizer') and arranger.grouping_optimizer:
        optimizer = arranger.grouping_optimizer
        print(f"✅ 智能分组优化器已初始化")
        print(f"  - production_mode: {getattr(optimizer, 'production_mode', 'N/A')}")
        print(f"  - canvas_width_cm: {getattr(optimizer, 'canvas_width_cm', 'N/A')}")
        
        # 执行智能分组优化
        print(f"\n🔍 执行智能分组优化...")
        optimized_images = optimizer.optimize_image_grouping(test_images)
        
        # 检查优化结果
        rotation_suggested_count = 0
        for img in optimized_images:
            if img.get('intelligent_rotation_suggested', False):
                rotation_suggested_count += 1
                print(f"  ✅ {img['pattern_name']}: 建议旋转 - {img.get('optimization_reason', '')}")
            else:
                print(f"  ❌ {img['pattern_name']}: 不建议旋转")
        
        print(f"\n📊 智能分组优化结果：{rotation_suggested_count}/3 张建议旋转")
        
        if rotation_suggested_count == 3:
            print(f"✅ 智能分组优化器工作正常")
        else:
            print(f"❌ 智能分组优化器可能有问题")
            return
    else:
        print(f"❌ 智能分组优化器未初始化")
        return
    
    # 步骤2：检查UnifiedImageArranger的处理
    print(f"\n" + "=" * 60)
    print("步骤2：UnifiedImageArranger处理")
    print("=" * 60)
    
    # 模拟arrange_images的处理过程
    print(f"🔧 模拟图片排列过程...")
    
    # 检查第一张图片的处理
    first_image = optimized_images[0]
    print(f"\n📋 处理第一张图片：{first_image['pattern_name']}")
    print(f"  - 原始尺寸：{first_image['width_cm']}x{first_image['height_cm']}cm")
    print(f"  - intelligent_rotation_suggested: {first_image.get('intelligent_rotation_suggested', False)}")
    print(f"  - optimization_reason: {first_image.get('optimization_reason', 'N/A')}")
    
    # 转换为像素
    width_px = int(first_image['width_cm'] * 72 / 2.54)
    height_px = int(first_image['height_cm'] * 72 / 2.54)
    print(f"  - 像素尺寸：{width_px}x{height_px}px")
    
    # 检查RectPack排列器
    print(f"\n" + "=" * 60)
    print("步骤3：RectPack排列器处理")
    print("=" * 60)
    
    if hasattr(arranger, 'rectpack_arranger') and arranger.rectpack_arranger:
        rectpack_arranger = arranger.rectpack_arranger
        print(f"✅ RectPack排列器已初始化")
        print(f"  - rotation_enabled: {getattr(rectpack_arranger, 'rotation_enabled', 'N/A')}")
        print(f"  - bin_width: {getattr(rectpack_arranger, 'bin_width', 'N/A')}px")
        
        # 准备图片数据（模拟UnifiedImageArranger的处理）
        image_data = {
            'path': first_image['path'],
            'name': first_image['pattern_name'],
            'width_cm': first_image['width_cm'],
            'height_cm': first_image['height_cm'],
            'original_width_px': width_px,
            'original_height_px': height_px,
            'unique_id': f"{first_image['pattern_name']}_test",
            'pattern_data': first_image,
            'intelligent_rotation_suggested': first_image.get('intelligent_rotation_suggested', False),
            'optimization_reason': first_image.get('optimization_reason', '')
        }
        
        print(f"\n🔧 准备图片数据：")
        print(f"  - intelligent_rotation_suggested: {image_data['intelligent_rotation_suggested']}")
        print(f"  - optimization_reason: {image_data['optimization_reason']}")
        
        # 测试预处理尺寸
        print(f"\n🧪 测试预处理尺寸...")
        processed_dims = rectpack_arranger._preprocess_image_dimensions(width_px, height_px, image_data)
        
        if processed_dims:
            width_with_spacing, height_with_spacing, was_rotated = processed_dims
            print(f"  ✅ 预处理成功：")
            print(f"    - 输入尺寸：{width_px}x{height_px}px")
            print(f"    - 输出尺寸：{width_with_spacing}x{height_with_spacing}px")
            print(f"    - 是否旋转：{was_rotated}")
            
            if was_rotated:
                print(f"  ✅ RectPack排列器正确应用了旋转")
            else:
                print(f"  ❌ RectPack排列器没有应用旋转")
                
                # 进一步调试
                print(f"\n🔍 调试旋转决策逻辑...")
                upper_rotation_needed = image_data.get('need_rotation', False)
                intelligent_rotation_suggested = image_data.get('intelligent_rotation_suggested', False)
                effective_rotation_needed = intelligent_rotation_suggested or upper_rotation_needed
                rotation_enabled = getattr(rectpack_arranger, 'rotation_enabled', False)
                
                print(f"    - upper_rotation_needed: {upper_rotation_needed}")
                print(f"    - intelligent_rotation_suggested: {intelligent_rotation_suggested}")
                print(f"    - effective_rotation_needed: {effective_rotation_needed}")
                print(f"    - rotation_enabled: {rotation_enabled}")
                
                # 检查尺寸约束
                bin_width = getattr(rectpack_arranger, 'bin_width', 0)
                bin_height = getattr(rectpack_arranger, 'bin_height', 0)
                
                print(f"    - bin_width: {bin_width}px")
                print(f"    - bin_height: {bin_height}px")
                
                # 检查旋转后是否能放置
                can_fit_rotated = (height_px <= bin_width and (bin_height == 0 or width_px <= bin_height))
                can_fit_original = (width_px <= bin_width and (bin_height == 0 or height_px <= bin_height))
                
                print(f"    - 原始方向能放置：{can_fit_original}")
                print(f"    - 旋转后能放置：{can_fit_rotated}")
                
                if effective_rotation_needed and rotation_enabled and can_fit_rotated:
                    print(f"  ❓ 条件满足但没有旋转，可能是逻辑问题")
                elif not effective_rotation_needed:
                    print(f"  ❓ 没有旋转需求，检查智能分组优化器")
                elif not rotation_enabled:
                    print(f"  ❓ 旋转功能被禁用")
                elif not can_fit_rotated:
                    print(f"  ❓ 旋转后无法放置")
        else:
            print(f"  ❌ 预处理失败")
    else:
        print(f"❌ RectPack排列器未初始化")
    
    # 步骤4：完整排列测试
    print(f"\n" + "=" * 60)
    print("步骤4：完整排列测试")
    print("=" * 60)
    
    print(f"🚀 执行完整的图片排列...")
    try:
        arranged_images = arranger.arrange_images(test_images)
        
        print(f"📊 排列结果：")
        print(f"  - 成功排列：{len(arranged_images)}/3 张")
        
        rotation_count = 0
        for img in arranged_images:
            need_rotation = img.get('need_rotation', False)
            if need_rotation:
                rotation_count += 1
            print(f"  - {img.get('pattern_name', 'Unknown')}: "
                  f"位置({img.get('x', 0)},{img.get('y', 0)}) "
                  f"尺寸{img.get('width', 0)}x{img.get('height', 0)}px "
                  f"旋转:{need_rotation}")
        
        print(f"\n📈 最终统计：")
        print(f"  - 旋转图片：{rotation_count}/3 张")
        
        if rotation_count == 3:
            print(f"  ✅ 所有图片都被正确旋转")
        elif rotation_count > 0:
            print(f"  ⚠️ 部分图片被旋转")
        else:
            print(f"  ❌ 没有图片被旋转")
            
    except Exception as e:
        print(f"❌ 排列过程出错：{str(e)}")

if __name__ == "__main__":
    print("旋转流程调试工具")
    print("追踪智能旋转建议的完整传递路径")
    
    debug_rotation_flow()
    
    print(f"\n" + "=" * 80)
    print("调试完成")
    print("=" * 80)
