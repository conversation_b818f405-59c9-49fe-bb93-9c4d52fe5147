#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
约束管理器核心功能测试
专门测试宽度间隙限制和边长微调功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.rectpack_constraint_manager import RectPackConstraintManager

def test_width_gap_constraint_fixed():
    """测试修正后的宽度间隙约束检查"""
    print("=" * 60)
    print("测试修正后的宽度间隙约束检查")
    print("=" * 60)
    
    manager = RectPackConstraintManager(
        canvas_width_cm=163.0,
        max_width_gap_cm=10.0
    )
    
    test_cases = [
        {
            'name': '正常情况：3张51x137cm图片（旋转后）',
            'images': [{'width': 1446, 'height': 3883}] * 3,  # 51x137cm
            'expected': '✅ 间隙10cm，刚好符合限制'
        },
        {
            'name': '超出画布：3张137x51cm图片（原始方向）',
            'images': [{'width': 3883, 'height': 1446}] * 3,  # 137x51cm
            'expected': '❌ 总宽度411cm超出163cm画布'
        },
        {
            'name': '间隙过大：1张100x50cm图片',
            'images': [{'width': 2835, 'height': 1417}],  # 100x50cm
            'expected': '❌ 间隙63cm超过10cm限制'
        },
        {
            'name': '符合要求：4张40x30cm图片',
            'images': [{'width': 1134, 'height': 850}] * 4,  # 40x30cm
            'expected': '✅ 间隙3cm，符合限制'
        }
    ]
    
    for case in test_cases:
        print(f"\n测试案例: {case['name']}")
        print(f"预期结果: {case['expected']}")
        
        violation = manager.check_width_gap_constraint(case['images'])
        
        if violation:
            print(f"实际结果: ❌ {violation}")
        else:
            # 计算实际间隙
            total_width_cm = sum(img['width'] * 2.54 / 72 for img in case['images'])
            gap_cm = 163.0 - total_width_cm
            print(f"实际结果: ✅ 无违反，间隙 {gap_cm:.1f}cm")

def test_edge_reduction_scenarios():
    """测试不同边长微调场景"""
    print("\n" + "=" * 60)
    print("测试不同边长微调场景")
    print("=" * 60)
    
    manager = RectPackConstraintManager(
        canvas_width_cm=163.0,
        max_edge_reduction_cm=1.0
    )
    
    test_images = [
        {
            'name': '大图片（优先缩减宽度）',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'expected': '缩减宽度1cm'
        },
        {
            'name': '高图片（优先缩减高度）',
            'width_cm': 40.0,
            'height_cm': 80.0,
            'expected': '缩减高度1cm'
        },
        {
            'name': '正方形图片',
            'width_cm': 50.0,
            'height_cm': 50.0,
            'expected': '缩减宽度1cm'
        },
        {
            'name': '小图片（按比例缩减）',
            'width_cm': 20.0,
            'height_cm': 15.0,
            'expected': '缩减宽度0.4cm'
        }
    ]
    
    for i, test_img in enumerate(test_images):
        print(f"\n测试图片 {i+1}: {test_img['name']}")
        print(f"原始尺寸: {test_img['width_cm']}x{test_img['height_cm']}cm")
        print(f"预期: {test_img['expected']}")
        
        image_data = {
            'unique_id': f'test_img_{i+1}',
            'name': test_img['name']
        }
        
        adjusted_width, adjusted_height, was_adjusted = manager.apply_edge_reduction_strategy(
            test_img['width_cm'], test_img['height_cm'], image_data
        )
        
        if was_adjusted:
            width_reduction = test_img['width_cm'] - adjusted_width
            height_reduction = test_img['height_cm'] - adjusted_height
            print(f"实际结果: ✅ 调整为 {adjusted_width:.1f}x{adjusted_height:.1f}cm")
            print(f"  缩减: 宽度{width_reduction:.1f}cm, 高度{height_reduction:.1f}cm")
        else:
            print(f"实际结果: ➡️ 无需调整")

def test_optimal_adjustments():
    """测试最优调整方案计算"""
    print("\n" + "=" * 60)
    print("测试最优调整方案计算")
    print("=" * 60)
    
    manager = RectPackConstraintManager(
        canvas_width_cm=163.0,
        max_width_gap_cm=10.0,
        max_edge_reduction_cm=1.0
    )
    
    # 场景1：需要小幅调整
    print("场景1: 需要小幅调整")
    images_1 = [
        {'width_cm': 52.0, 'height_cm': 40.0, 'unique_id': 'img1', 'name': '图片1'},
        {'width_cm': 52.0, 'height_cm': 40.0, 'unique_id': 'img2', 'name': '图片2'},
        {'width_cm': 52.0, 'height_cm': 40.0, 'unique_id': 'img3', 'name': '图片3'},
    ]
    target_width_1 = 153.0  # 163 - 10 = 153cm
    current_width_1 = sum(img['width_cm'] for img in images_1)  # 156cm
    
    print(f"  当前总宽度: {current_width_1}cm")
    print(f"  目标总宽度: {target_width_1}cm")
    print(f"  需要缩减: {current_width_1 - target_width_1}cm")
    
    adjustments_1 = manager.calculate_optimal_adjustments(images_1, target_width_1)
    
    print("  调整方案:")
    for adj in adjustments_1:
        if adj.get_total_reduction() > 0:
            print(f"    - {adj}")
    
    optimized_width_1 = sum(adj.adjusted_width for adj in adjustments_1)
    print(f"  优化后总宽度: {optimized_width_1:.1f}cm")
    print(f"  是否达到目标: {'✅' if optimized_width_1 <= target_width_1 else '❌'}")
    
    # 场景2：需要大幅调整（超出能力）
    print("\n场景2: 需要大幅调整（超出能力）")
    images_2 = [
        {'width_cm': 60.0, 'height_cm': 40.0, 'unique_id': 'img1', 'name': '大图片1'},
        {'width_cm': 60.0, 'height_cm': 40.0, 'unique_id': 'img2', 'name': '大图片2'},
        {'width_cm': 60.0, 'height_cm': 40.0, 'unique_id': 'img3', 'name': '大图片3'},
    ]
    target_width_2 = 153.0  # 163 - 10 = 153cm
    current_width_2 = sum(img['width_cm'] for img in images_2)  # 180cm
    
    print(f"  当前总宽度: {current_width_2}cm")
    print(f"  目标总宽度: {target_width_2}cm")
    print(f"  需要缩减: {current_width_2 - target_width_2}cm")
    
    adjustments_2 = manager.calculate_optimal_adjustments(images_2, target_width_2)
    
    print("  调整方案:")
    for adj in adjustments_2:
        if adj.get_total_reduction() > 0:
            print(f"    - {adj}")
    
    optimized_width_2 = sum(adj.adjusted_width for adj in adjustments_2)
    print(f"  优化后总宽度: {optimized_width_2:.1f}cm")
    print(f"  是否达到目标: {'✅' if optimized_width_2 <= target_width_2 else '❌'}")
    print(f"  剩余缺口: {optimized_width_2 - target_width_2:.1f}cm")

def test_constraint_statistics():
    """测试约束统计功能"""
    print("\n" + "=" * 60)
    print("测试约束统计功能")
    print("=" * 60)
    
    manager = RectPackConstraintManager(
        canvas_width_cm=163.0,
        max_width_gap_cm=10.0,
        max_edge_reduction_cm=1.0
    )
    
    # 执行一系列操作来生成统计数据
    print("执行一系列操作...")
    
    # 检查约束（会增加检查次数）
    test_images = [
        [{'width': 3883, 'height': 1446}] * 3,  # 违反约束
        [{'width': 1446, 'height': 3883}] * 3,  # 符合约束
        [{'width': 2835, 'height': 1417}],      # 违反约束
    ]
    
    for images in test_images:
        violation = manager.check_width_gap_constraint(images)
        if violation:
            print(f"  发现违反: {violation.constraint_type}")
    
    # 应用边长调整（会增加调整次数）
    test_adjustments = [
        {'width_cm': 137.0, 'height_cm': 51.0, 'unique_id': 'test1'},
        {'width_cm': 100.0, 'height_cm': 80.0, 'unique_id': 'test2'},
        {'width_cm': 50.0, 'height_cm': 50.0, 'unique_id': 'test3'},
    ]
    
    for adj_data in test_adjustments:
        manager.apply_edge_reduction_strategy(
            adj_data['width_cm'], adj_data['height_cm'], adj_data
        )
    
    # 获取统计信息
    stats = manager.get_constraint_statistics()
    
    print("\n约束管理器统计信息:")
    print(f"  - 约束检查次数: {stats['constraint_checks']}")
    print(f"  - 约束违反次数: {stats['constraint_violations']}")
    print(f"  - 违反率: {stats['violation_rate']:.1%}")
    print(f"  - 应用调整次数: {stats['adjustments_applied']}")
    print(f"  - 总调整记录: {stats['total_adjustments']}")
    print(f"  - 调整率: {stats['adjustment_rate']:.1%}")

if __name__ == "__main__":
    print("约束管理器核心功能测试")
    print("测试宽度间隙限制和边长微调功能")
    
    # 测试1：修正后的宽度间隙约束检查
    test_width_gap_constraint_fixed()
    
    # 测试2：不同边长微调场景
    test_edge_reduction_scenarios()
    
    # 测试3：最优调整方案计算
    test_optimal_adjustments()
    
    # 测试4：约束统计功能
    test_constraint_statistics()
    
    print("\n" + "=" * 60)
    print("核心功能测试完成！")
    print("=" * 60)
