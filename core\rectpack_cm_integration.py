#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack CM单位算法集成模块
提供与现有系统的集成接口
"""

import logging
import time
from typing import List, Dict, Any, Optional

log = logging.getLogger(__name__)

class RectPackCMIntegration:
    """RectPack CM单位算法集成类"""

    def __init__(self):
        """初始化集成模块"""
        self.processor = None
        log.info("RectPack CM集成模块初始化")

    def process_pattern_items_cm(self, pattern_items: List[Dict[str, Any]],
                               canvas_width_cm: float, max_height_cm: float,
                               image_spacing_cm: float, horizontal_expansion_cm: float = 0.0,
                               ppi: int = 72, is_test_mode: bool = False,
                               output_directory: str = "") -> Dict[str, Any]:
        """
        处理图案项目（CM单位版本）

        Args:
            pattern_items: 图案项目列表
            canvas_width_cm: 画布宽度（cm）
            max_height_cm: 最大高度（cm）
            image_spacing_cm: 图片间距（cm）
            horizontal_expansion_cm: 水平扩展（cm）
            ppi: 像素密度
            is_test_mode: 是否测试模式
            output_directory: 输出目录

        Returns:
            Dict: 处理结果
        """
        log.info(f"开始处理{len(pattern_items)}个图案项目（CM单位）")
        start_time = time.time()

        try:
            # 创建处理配置
            import sys
            import os
            sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from core.rectpack_unified_processor import ProcessingConfig, RectPackUnifiedProcessor

            config = ProcessingConfig(
                canvas_width_cm=canvas_width_cm,
                max_height_cm=max_height_cm,
                image_spacing_cm=image_spacing_cm,
                horizontal_expansion_cm=horizontal_expansion_cm,
                ppi=ppi,
                is_test_mode=is_test_mode,
                output_directory=output_directory
            )

            # 创建处理器
            self.processor = RectPackUnifiedProcessor(config)

            # 执行处理
            result = self.processor.process_images(pattern_items)

            # 转换为兼容格式
            compatible_result = self._convert_to_compatible_format(result, config)

            processing_time = time.time() - start_time
            log.info(f"CM单位处理完成，耗时{processing_time:.2f}秒")

            return compatible_result

        except Exception as e:
            log.error(f"CM单位处理失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }

    def _convert_to_compatible_format(self, result: Dict[str, Any],
                                    config) -> Dict[str, Any]:
        """转换为兼容格式"""
        if not result['success']:
            return result

        compatible_result = {
            'success': True,
            'mode': 'rectpack_cm',
            'algorithm': 'RectPack CM单位算法',
            'processing_time': result.get('processing_time', 0),
            'canvas_count': result.get('canvas_count', 0)
        }

        # 添加统计信息
        if 'stats' in result:
            stats = result['stats']
            compatible_result.update({
                'total_images': stats['total_images'],
                'overall_utilization': stats['overall_utilization'],
                'average_utilization': stats['average_utilization'],
                'best_canvas_utilization': stats['best_canvas_utilization'],
                'worst_canvas_utilization': stats['worst_canvas_utilization'],
                'canvas_details': stats.get('canvas_details', [])
            })

        # 根据模式添加特定结果
        if config.is_test_mode:
            compatible_result.update({
                'test_canvases': result.get('canvases', []),
                'visualization_data': result.get('visualization_data', []),
                'documentation': result.get('documentation', '')
            })
        else:
            compatible_result.update({
                'ps_results': result.get('ps_results', {}),
                'tiff_files': result.get('tiff_files', []),
                'documentation': result.get('documentation', '')
            })

        return compatible_result

    def get_algorithm_info(self) -> Dict[str, Any]:
        """获取算法信息"""
        return {
            'name': 'RectPack CM单位算法',
            'version': '1.0.0',
            'description': '使用cm单位进行计算的RectPack算法，只在PS处理时转换为px单位',
            'features': [
                '整个计算过程使用cm单位',
                '测试模式和生产模式统一逻辑',
                'PS处理时精确单位转换',
                '支持图片旋转和边长缩减',
                '多阶段优化算法',
                '高利用率目标（90%+）'
            ],
            'parameters': {
                'canvas_width_cm': '画布宽度（cm）',
                'max_height_cm': '最大高度（cm）',
                'image_spacing_cm': '图片间距（cm）',
                'horizontal_expansion_cm': '水平扩展（cm）',
                'ppi': '像素密度（用于PS转换）',
                'is_test_mode': '测试模式开关'
            }
        }


# 兼容性函数，用于替换现有的RectPack调用
def process_rectpack_cm_layout(pattern_items: List[Dict[str, Any]],
                             canvas_width_cm: float, max_height_cm: float,
                             image_spacing_cm: float, horizontal_expansion_cm: float = 0.0,
                             ppi: int = 72, is_test_mode: bool = False,
                             output_directory: str = "") -> Dict[str, Any]:
    """
    RectPack CM单位布局处理函数（兼容性接口）

    Args:
        pattern_items: 图案项目列表
        canvas_width_cm: 画布宽度（cm）
        max_height_cm: 最大高度（cm）
        image_spacing_cm: 图片间距（cm）
        horizontal_expansion_cm: 水平扩展（cm）
        ppi: 像素密度
        is_test_mode: 是否测试模式
        output_directory: 输出目录

    Returns:
        Dict: 处理结果
    """
    integration = RectPackCMIntegration()
    return integration.process_pattern_items_cm(
        pattern_items=pattern_items,
        canvas_width_cm=canvas_width_cm,
        max_height_cm=max_height_cm,
        image_spacing_cm=image_spacing_cm,
        horizontal_expansion_cm=horizontal_expansion_cm,
        ppi=ppi,
        is_test_mode=is_test_mode,
        output_directory=output_directory
    )


def get_rectpack_cm_algorithm_info() -> Dict[str, Any]:
    """获取RectPack CM算法信息"""
    integration = RectPackCMIntegration()
    return integration.get_algorithm_info()


# 测试函数
def test_integration():
    """测试集成功能"""
    print("测试RectPack CM集成功能")
    print("=" * 40)

    # 测试数据
    test_pattern_items = [
        {'width_cm': 190, 'height_cm': 82, 'name': 'test1', 'path': 'test1.jpg'},
        {'width_cm': 163, 'height_cm': 123, 'name': 'test2', 'path': 'test2.jpg'},
        {'width_cm': 137, 'height_cm': 51, 'name': 'test3', 'path': 'test3.jpg'},
        {'width_cm': 123, 'height_cm': 52, 'name': 'test4', 'path': 'test4.jpg'}
    ]

    # 测试兼容性接口
    result = process_rectpack_cm_layout(
        pattern_items=test_pattern_items,
        canvas_width_cm=163.0,
        max_height_cm=5000.0,
        image_spacing_cm=1.0,
        horizontal_expansion_cm=0.0,
        ppi=72,
        is_test_mode=True,
        output_directory="integration_test"
    )

    if result['success']:
        print("✅ 集成测试成功")
        print(f"算法: {result['algorithm']}")
        print(f"画布数量: {result['canvas_count']}")
        print(f"整体利用率: {result.get('overall_utilization', 0):.2f}%")
        print(f"处理时间: {result['processing_time']:.2f}秒")
    else:
        print(f"❌ 集成测试失败: {result.get('error', '未知错误')}")

    # 测试算法信息
    info = get_rectpack_cm_algorithm_info()
    print(f"\n算法信息:")
    print(f"名称: {info['name']}")
    print(f"版本: {info['version']}")
    print(f"描述: {info['description']}")
    print(f"特性数量: {len(info['features'])}")


if __name__ == "__main__":
    test_integration()
