#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法参数模块 - 优化版
添加简单缓存机制，减少重复数据库访问，同时确保参数及时更新
"""
from rectpack import SORT_AREA, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO
from rectpack import MaxRectsBssf, MaxRectsBaf, MaxRectsBlsf
import logging
import time
import threading

log = logging.getLogger(__name__)

# 简单的缓存机制
_params_cache = None
_cache_timestamp = 0
_cache_lock = threading.Lock()
CACHE_DURATION = 5  # 缓存5秒，平衡性能和实时性

def get_current_rectpack_params():
    """获取当前RectPack参数，带简单缓存优化"""
    global _params_cache, _cache_timestamp

    current_time = time.time()

    # 检查缓存是否有效
    with _cache_lock:
        if _params_cache is not None and (current_time - _cache_timestamp) < CACHE_DURATION:
            return _params_cache.copy()  # 返回副本避免修改原缓存

    # 缓存过期或不存在，重新获取参数
    params = _fetch_params_from_database()

    # 更新缓存
    with _cache_lock:
        _params_cache = params.copy()
        _cache_timestamp = current_time

    return params

def clear_params_cache():
    """清除参数缓存，强制下次重新读取"""
    global _params_cache, _cache_timestamp
    with _cache_lock:
        _params_cache = None
        _cache_timestamp = 0

def _fetch_params_from_database():
    """从数据库获取参数"""
    try:
        from utils.config_manager_duckdb import ConfigManagerDuckDB
        config_manager = ConfigManagerDuckDB()
        settings = config_manager.get_rectpack_settings()

        # 排序算法映射
        sort_map = {
            0: SORT_AREA,    # 面积排序
            1: SORT_AREA,    # 周长排序（暂用面积）
            2: SORT_DIFF,    # 差值排序
            3: SORT_SSIDE,   # 短边排序
            4: SORT_LSIDE,   # 长边排序
            5: SORT_RATIO    # 比例排序
        }

        # 装箱算法映射
        pack_map = {
            0: MaxRectsBssf,  # BSSF
            1: MaxRectsBaf,   # BFF
            2: MaxRectsBlsf   # BBF
        }

        # 优化迭代次数，限制在合理范围内
        optimization_iterations = settings.get('rectpack_optimization_iterations', 15)
        # 限制迭代次数在 3-25 之间，避免过度优化
        optimization_iterations = max(3, min(25, optimization_iterations))

        return {
            # 核心参数
            'rotation': settings.get('rectpack_rotation_enabled', True),
            'sort_algo': sort_map.get(settings.get('rectpack_sort_strategy', 3), SORT_SSIDE),
            'pack_algo': pack_map.get(settings.get('rectpack_pack_algorithm', 1), MaxRectsBaf),

            # 优化参数（限制迭代次数）
            'optimization_iterations': optimization_iterations,
            'min_utilization_threshold': settings.get('rectpack_min_utilization_threshold', 95.0),
            'rotation_penalty': settings.get('rectpack_rotation_penalty', 0.001),
            'enable_edge_shrink': settings.get('rectpack_enable_edge_shrink', True),
            'edge_shrink_tolerance_cm': settings.get('rectpack_edge_shrink_tolerance_cm', 0.8),
            'enable_gap_filling': settings.get('rectpack_enable_gap_filling', True),
            'enable_multi_stage_optimization': settings.get('rectpack_enable_multi_stage_optimization', True)
        }

    except Exception as e:
        log.error(f"获取RectPack参数失败: {str(e)}，使用默认值")
        return {
            'rotation': True,
            'sort_algo': SORT_SSIDE,
            'pack_algo': MaxRectsBaf,
            'optimization_iterations': 10,  # 默认使用中等迭代次数
            'min_utilization_threshold': 92.0,  # 默认目标稍低一些
            'rotation_penalty': 0.005,
            'enable_edge_shrink': True,
            'edge_shrink_tolerance_cm': 0.5,
            'enable_gap_filling': True,
            'enable_multi_stage_optimization': False  # 默认关闭多阶段优化
        }

# 为高级设置提供的快捷函数
def force_refresh_params():
    """强制刷新参数，用于高级设置修改后立即生效"""
    clear_params_cache()
    return get_current_rectpack_params()

def get_optimization_summary():
    """获取优化参数摘要，用于日志显示"""
    params = get_current_rectpack_params()
    return {
        'rotation_enabled': params['rotation'],
        'optimization_iterations': params['optimization_iterations'],
        'utilization_target': params['min_utilization_threshold'],
        'multi_stage': params['enable_multi_stage_optimization']
    }
