#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真实生产场景测试脚本
基于用户提供的生产报告，测试137x51cm图片的旋转优化问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.unified_image_arranger import UnifiedImageArranger
from core.intelligent_grouping_optimizer import IntelligentGroupingOptimizer

def test_137x51_scenario():
    """测试137x51cm图片场景（基于用户生产报告1）"""
    print("=" * 80)
    print("真实生产场景测试：137x51cm图片优化")
    print("=" * 80)
    
    # 基于用户报告的实际数据
    print("基于用户生产报告的实际场景：")
    print("- 材质名称: 6.2假硅藻泥 (1)_RPA操作表格")
    print("- 基础宽度: 163cm")
    print("- 水平拓展: 3cm") 
    print("- 实际宽度: 166cm")
    print("- 137x51cm图片: 17张")
    print("- 59x35cm图片: 4张")
    print("- 当前利用率: 81.34%")
    print("- 目标利用率: 93%+")
    
    # 创建测试数据（模拟用户实际场景）
    test_images = []
    
    # 137x51cm图片（17张）
    for i in range(17):
        test_images.append({
            'pattern_name': f'137x51_图片_{i+1}',
            'path': f'137x51_{i+1}.jpg',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'quantity': 1,
            'index': i,
            'row_number': 1
        })
    
    # 59x35cm图片（4张）
    for i in range(4):
        test_images.append({
            'pattern_name': f'59x35_图片_{i+1}',
            'path': f'59x35_{i+1}.jpg',
            'width_cm': 59.0,
            'height_cm': 35.0,
            'quantity': 1,
            'index': 17 + i,
            'row_number': 1
        })
    
    print(f"\n测试数据：{len(test_images)} 张图片")
    print(f"- 137x51cm: 17张")
    print(f"- 59x35cm: 4张")
    
    # 测试不同的画布宽度
    canvas_scenarios = [
        {"name": "基础宽度", "width_cm": 163.0, "description": "不含水平拓展"},
        {"name": "实际宽度", "width_cm": 166.0, "description": "含3cm水平拓展"},
    ]
    
    for scenario in canvas_scenarios:
        print(f"\n" + "=" * 60)
        print(f"测试场景：{scenario['name']} ({scenario['description']})")
        print(f"画布宽度：{scenario['width_cm']}cm")
        print("=" * 60)
        
        # 直接测试智能分组优化器
        print(f"\n🔍 智能分组优化分析：")
        
        optimizer = IntelligentGroupingOptimizer(
            canvas_width_cm=scenario['width_cm'],
            target_utilization=0.93,
            production_mode=True,  # 启用生产模式
            log_signal=None
        )
        
        optimized_images = optimizer.optimize_image_grouping(test_images)
        
        # 分析137x51cm图片的优化结果
        optimized_137x51 = [img for img in optimized_images 
                           if img.get('width_cm') == 137.0 and img.get('height_cm') == 51.0]
        
        rotation_suggested_137x51 = sum(1 for img in optimized_137x51 
                                      if img.get('intelligent_rotation_suggested', False))
        
        print(f"\n📊 137x51cm图片优化结果：")
        print(f"  - 总数量：{len(optimized_137x51)} 张")
        print(f"  - 建议旋转：{rotation_suggested_137x51} 张")
        print(f"  - 旋转比例：{rotation_suggested_137x51/len(optimized_137x51)*100:.1f}%")
        
        if rotation_suggested_137x51 > 0:
            print(f"  ✅ 成功识别旋转优化机会！")
            # 显示优化原因
            for img in optimized_137x51:
                if img.get('intelligent_rotation_suggested', False):
                    reason = img.get('optimization_reason', '')
                    print(f"    - {reason}")
                    break  # 只显示一个示例
        else:
            print(f"  ❌ 未识别到旋转优化机会")
            
        # 手动计算预期效果
        print(f"\n🧮 手动计算验证：")
        
        # 原始方向计算
        original_fit = int(scenario['width_cm'] / 137.0)
        original_utilization = (original_fit * 137.0) / scenario['width_cm']
        
        # 旋转后计算
        rotated_fit = int(scenario['width_cm'] / 51.0)
        rotated_utilization = (rotated_fit * 51.0) / scenario['width_cm']
        
        print(f"  原始方向 (137x51cm):")
        print(f"    - 横向放置：{original_fit} 张/行")
        print(f"    - 利用率：{original_utilization:.1%}")
        
        print(f"  旋转后 (51x137cm):")
        print(f"    - 横向放置：{rotated_fit} 张/行")
        print(f"    - 利用率：{rotated_utilization:.1%}")
        
        if rotated_utilization > original_utilization:
            improvement = rotated_utilization / original_utilization
            print(f"  💡 旋转优势：利用率提升 {improvement:.1f}x，应该被优化！")
        else:
            print(f"  ⚠️ 旋转无优势")

def test_67x52_scenario():
    """测试67x52cm图片场景（基于用户生产报告2）"""
    print(f"\n" + "=" * 80)
    print("真实生产场景测试：67x52cm图片优化")
    print("=" * 80)
    
    print("基于用户生产报告2的场景：")
    print("- 67x52cm图片: 30张")
    print("- 画布宽度: 166cm（假设）")
    
    # 创建测试数据
    test_images = []
    for i in range(30):
        test_images.append({
            'pattern_name': f'67x52_图片_{i+1}',
            'path': f'67x52_{i+1}.jpg',
            'width_cm': 67.0,
            'height_cm': 52.0,
            'quantity': 1,
            'index': i,
            'row_number': 1
        })
    
    print(f"\n测试数据：{len(test_images)} 张 67x52cm 图片")
    
    # 测试智能分组优化器
    optimizer = IntelligentGroupingOptimizer(
        canvas_width_cm=166.0,
        target_utilization=0.93,
        production_mode=True,
        log_signal=None
    )
    
    optimized_images = optimizer.optimize_image_grouping(test_images)
    
    # 分析优化结果
    rotation_suggested = sum(1 for img in optimized_images 
                           if img.get('intelligent_rotation_suggested', False))
    
    print(f"\n📊 67x52cm图片优化结果：")
    print(f"  - 建议旋转：{rotation_suggested}/{len(optimized_images)} 张")
    
    if rotation_suggested > 0:
        print(f"  ✅ 成功识别旋转优化机会！")
        for img in optimized_images:
            if img.get('intelligent_rotation_suggested', False):
                reason = img.get('optimization_reason', '')
                print(f"    - {reason}")
                break
    else:
        print(f"  ❌ 未识别到旋转优化机会")
    
    # 手动计算
    print(f"\n🧮 手动计算验证：")
    
    # 原始方向
    original_fit = int(166.0 / 67.0)
    original_utilization = (original_fit * 67.0) / 166.0
    
    # 旋转后
    rotated_fit = int(166.0 / 52.0)
    rotated_utilization = (rotated_fit * 52.0) / 166.0
    
    print(f"  原始方向 (67x52cm): {original_fit}张/行, 利用率{original_utilization:.1%}")
    print(f"  旋转后 (52x67cm): {rotated_fit}张/行, 利用率{rotated_utilization:.1%}")
    
    if rotated_utilization > original_utilization:
        improvement = rotated_utilization / original_utilization
        print(f"  💡 旋转优势：利用率提升 {improvement:.1f}x")

def test_debug_grouping_threshold():
    """调试分组阈值问题"""
    print(f"\n" + "=" * 80)
    print("调试：分组数量阈值测试")
    print("=" * 80)
    
    # 测试不同数量的图片分组
    test_counts = [1, 2, 3, 5, 10, 17]
    
    for count in test_counts:
        print(f"\n测试 {count} 张 137x51cm 图片：")
        
        test_images = []
        for i in range(count):
            test_images.append({
                'pattern_name': f'test_{i+1}',
                'path': f'test_{i+1}.jpg',
                'width_cm': 137.0,
                'height_cm': 51.0,
                'quantity': 1,
                'index': i,
                'row_number': 1
            })
        
        optimizer = IntelligentGroupingOptimizer(
            canvas_width_cm=163.0,
            target_utilization=0.93,
            production_mode=True,
            log_signal=None
        )
        
        optimized_images = optimizer.optimize_image_grouping(test_images)
        
        rotation_suggested = sum(1 for img in optimized_images 
                               if img.get('intelligent_rotation_suggested', False))
        
        print(f"  - 建议旋转：{rotation_suggested}/{count} 张")
        
        if count >= 3 and rotation_suggested == 0:
            print(f"  ⚠️ 警告：{count}张图片应该满足≥3的阈值，但未被优化！")
        elif count >= 3 and rotation_suggested > 0:
            print(f"  ✅ 正常：{count}张图片被正确优化")

if __name__ == "__main__":
    print("真实生产场景测试")
    print("基于用户提供的生产报告，验证137x51cm和67x52cm图片的旋转优化")
    
    # 测试1：137x51cm场景
    test_137x51_scenario()
    
    # 测试2：67x52cm场景  
    test_67x52_scenario()
    
    # 测试3：调试分组阈值
    test_debug_grouping_threshold()
    
    print(f"\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    print("如果137x51cm图片没有被建议旋转，可能的原因：")
    print("1. 画布宽度计算问题（163cm vs 166cm）")
    print("2. 分组数量阈值问题（需要≥3张）")
    print("3. 生产模式未正确启用")
    print("4. 优化判断逻辑需要进一步调整")
