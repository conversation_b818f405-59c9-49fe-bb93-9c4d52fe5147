#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能分组优化器
用于优化rectpack算法的画布利用率，通过智能分组和旋转策略提升横向空间利用率

特性：
1. 自动识别相同或相似尺寸的图片
2. 计算最优的旋转和分组策略
3. 基于数学计算，不硬编码特定尺寸
4. 目标是达到93%以上的横向利用率
"""

import logging
from typing import List, Dict, Any, Tuple, Optional
from collections import defaultdict
import math

# 配置日志
log = logging.getLogger(__name__)

class ImageGroup:
    """图片分组类"""
    
    def __init__(self, width_cm: float, height_cm: float):
        self.width_cm = width_cm
        self.height_cm = height_cm
        self.images = []
        self.optimal_rotation = False
        self.optimal_group_size = 1
        self.horizontal_utilization = 0.0
        self.total_width_needed = 0.0
        
    def add_image(self, image: Dict[str, Any]):
        """添加图片到分组"""
        self.images.append(image)
        
    def size(self) -> int:
        """返回分组中的图片数量"""
        return len(self.images)
        
    def get_dimensions(self, rotated: bool = False) -> Tuple[float, float]:
        """获取图片尺寸（考虑旋转）"""
        if rotated:
            return self.height_cm, self.width_cm
        return self.width_cm, self.height_cm

class IntelligentGroupingOptimizer:
    """智能分组优化器"""
    
    def __init__(self, canvas_width_cm: float, target_utilization: float = 0.93,
                 similarity_threshold: float = 0.1, log_signal=None, production_mode: bool = False):
        """
        初始化智能分组优化器

        Args:
            canvas_width_cm: 画布宽度（厘米）
            target_utilization: 目标利用率（默认93%）
            similarity_threshold: 尺寸相似度阈值（默认10%）
            log_signal: 日志信号
            production_mode: 生产模式，启用更宽松的优化策略
        """
        self.canvas_width_cm = canvas_width_cm
        self.target_utilization = target_utilization
        self.similarity_threshold = similarity_threshold
        self.log_signal = log_signal
        self.production_mode = production_mode

        # 生产模式下使用更宽松的优化策略
        if self.production_mode:
            self.min_utilization_threshold = 0.5  # 生产模式最低50%利用率即可优化
            self.min_improvement_ratio = 1.2      # 生产模式要求至少20%的相对提升
            self.emit_log("🏭 生产模式已启用：使用宽松优化策略")
        else:
            self.min_utilization_threshold = target_utilization
            self.min_improvement_ratio = 1.0
        
        # 分组结果
        self.image_groups = []
        self.optimization_results = {}
        
    def emit_log(self, message: str):
        """发送日志信息"""
        if self.log_signal:
            self.log_signal.emit(message)
        else:
            log.info(message)
    
    def optimize_image_grouping(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        优化图片分组和旋转策略

        Args:
            pattern_items: 原始图片列表

        Returns:
            List[Dict[str, Any]]: 优化后的图片列表（包含旋转建议）
        """
        self.emit_log("🔍 开始智能分组优化分析...")

        # 第零步：十位数优化机会分析
        tens_analysis = self.analyze_tens_digit_opportunities(pattern_items)

        # 第一步：分析图片尺寸分布
        size_groups = self._analyze_size_distribution(pattern_items)
        
        # 第二步：为每个尺寸组计算最优策略
        optimized_groups = []
        total_optimized_images = 0
        
        for size_key, group in size_groups.items():
            if group.size() >= 3:  # 只对有足够数量的分组进行优化
                optimization_result = self._calculate_optimal_strategy(group)
                if optimization_result['should_optimize']:
                    optimized_groups.append(optimization_result)
                    total_optimized_images += group.size()
                    
        # 第三步：应用优化策略
        optimized_pattern_items = self._apply_optimization_strategies(pattern_items, optimized_groups)
        
        # 输出优化结果统计
        self._log_optimization_summary(optimized_groups, total_optimized_images, len(pattern_items))
        
        return optimized_pattern_items
    
    def _analyze_size_distribution(self, pattern_items: List[Dict[str, Any]]) -> Dict[str, ImageGroup]:
        """分析图片尺寸分布，识别相同或相似尺寸的图片"""
        size_groups = {}
        
        for pattern in pattern_items:
            width_cm = pattern.get('width_cm', 0)
            height_cm = pattern.get('height_cm', 0)
            
            if width_cm <= 0 or height_cm <= 0:
                continue
                
            # 创建尺寸键（标准化到0.1cm精度）
            size_key = (round(width_cm, 1), round(height_cm, 1))
            
            if size_key not in size_groups:
                size_groups[size_key] = ImageGroup(width_cm, height_cm)
                
            size_groups[size_key].add_image(pattern)
        
        # 过滤掉数量太少的分组
        filtered_groups = {k: v for k, v in size_groups.items() if v.size() >= 2}
        
        self.emit_log(f"📊 尺寸分布分析完成:")
        self.emit_log(f"   - 总图片数: {len(pattern_items)}")
        self.emit_log(f"   - 不同尺寸: {len(size_groups)}")
        self.emit_log(f"   - 可优化分组: {len(filtered_groups)}")
        
        # 显示主要分组信息
        sorted_groups = sorted(filtered_groups.items(), key=lambda x: x[1].size(), reverse=True)
        for i, (size_key, group) in enumerate(sorted_groups[:5]):
            self.emit_log(f"   - 分组{i+1}: {size_key[0]}x{size_key[1]}cm, {group.size()}张")
            
        return filtered_groups
    
    def _calculate_optimal_strategy(self, group: ImageGroup) -> Dict[str, Any]:
        """计算分组的最优旋转和排列策略"""
        width_cm, height_cm = group.width_cm, group.height_cm
        
        # 计算原始方向的横向利用率
        original_fit_count = int(self.canvas_width_cm / width_cm)
        original_total_width = original_fit_count * width_cm
        original_utilization = original_total_width / self.canvas_width_cm
        
        # 计算旋转90度后的横向利用率
        rotated_fit_count = int(self.canvas_width_cm / height_cm)
        rotated_total_width = rotated_fit_count * height_cm
        rotated_utilization = rotated_total_width / self.canvas_width_cm
        
        # 选择更优的策略
        if rotated_utilization > original_utilization:
            optimal_rotation = True
            optimal_fit_count = rotated_fit_count
            optimal_utilization = rotated_utilization
            optimal_total_width = rotated_total_width
            strategy_desc = f"旋转90度: {height_cm}x{width_cm}cm"
        else:
            optimal_rotation = False
            optimal_fit_count = original_fit_count
            optimal_utilization = original_utilization
            optimal_total_width = original_total_width
            strategy_desc = f"保持原方向: {width_cm}x{height_cm}cm"
        
        # 判断是否值得优化（改进的优化策略）
        if self.production_mode:
            # 生产模式：更宽松的优化条件
            should_optimize = self._should_optimize_production_mode(
                optimal_utilization, original_utilization, optimal_fit_count, group
            )
        else:
            # 标准模式：原有的严格条件
            should_optimize = (
                optimal_utilization >= self.target_utilization and
                optimal_fit_count >= 2 and
                group.size() >= optimal_fit_count
            )
        
        result = {
            'group': group,
            'should_optimize': should_optimize,
            'optimal_rotation': optimal_rotation,
            'optimal_fit_count': optimal_fit_count,
            'optimal_utilization': optimal_utilization,
            'optimal_total_width': optimal_total_width,
            'strategy_desc': strategy_desc,
            'original_utilization': original_utilization,
            'rotated_utilization': rotated_utilization
        }
        
        if should_optimize:
            self.emit_log(f"✅ 找到优化策略: {width_cm}x{height_cm}cm ({group.size()}张)")
            self.emit_log(f"   - {strategy_desc}")
            self.emit_log(f"   - 横向放置: {optimal_fit_count}张/行")
            self.emit_log(f"   - 利用率: {optimal_utilization:.1%} (目标: {self.target_utilization:.1%})")
        
        return result

    def _should_optimize_production_mode(self, optimal_utilization: float, original_utilization: float,
                                       optimal_fit_count: int, group) -> bool:
        """
        生产模式下的优化判断逻辑（增强版）

        Args:
            optimal_utilization: 最优利用率
            original_utilization: 原始利用率
            optimal_fit_count: 最优放置数量
            group: 图片分组

        Returns:
            bool: 是否应该优化
        """
        # 条件1：基本可行性检查
        basic_feasible = (
            optimal_fit_count >= 2 and           # 至少能放2张
            group.size() >= optimal_fit_count    # 图片数量足够
        )

        if not basic_feasible:
            return False

        # 条件2：利用率检查（生产模式更宽松）
        utilization_acceptable = optimal_utilization >= self.min_utilization_threshold

        # 条件3：相对提升检查（旋转后比原来好）
        if original_utilization > 0:
            improvement_ratio = optimal_utilization / original_utilization
            relative_improvement = improvement_ratio >= self.min_improvement_ratio
        else:
            relative_improvement = True  # 原来无法放置，任何改善都是好的

        # 条件4：横向空间利用改善（特别针对用户场景）
        horizontal_improvement = optimal_fit_count > int(self.canvas_width_cm / group.width_cm)

        # 条件5：特殊场景强制优化（针对用户报告的具体问题）
        special_case_optimization = self._check_special_case_optimization(group, optimal_fit_count, optimal_utilization)

        # 新增条件6：紧密度评估（防止旋转后产生大面积空隙）
        compactness_acceptable = self._evaluate_rotation_compactness(group, optimal_fit_count, optimal_utilization)

        # 新的生产模式判断：满足基本可行性 + 紧密度可接受 + (显著改善条件)
        significant_improvement = (
            (utilization_acceptable and relative_improvement) or  # 利用率好且有提升
            special_case_optimization or                          # 特殊场景
            (horizontal_improvement and optimal_utilization >= 0.90)  # 横向改善且利用率很高
        )

        should_optimize = basic_feasible and compactness_acceptable and significant_improvement

        if should_optimize:
            reasons = []
            if utilization_acceptable:
                reasons.append(f"利用率{optimal_utilization:.1%}≥{self.min_utilization_threshold:.1%}")
            if relative_improvement:
                improvement_ratio = optimal_utilization / max(original_utilization, 0.01)
                reasons.append(f"相对提升{improvement_ratio:.1f}x≥{self.min_improvement_ratio:.1f}x")
            if horizontal_improvement:
                original_fit = int(self.canvas_width_cm / group.width_cm)
                reasons.append(f"横向改善{optimal_fit_count}张>{original_fit}张")
            if special_case_optimization:
                reasons.append(f"特殊场景优化")

            self.emit_log(f"🏭 生产模式优化: {group.width_cm}x{group.height_cm}cm ({', '.join(reasons)})")

        return should_optimize

    def _evaluate_rotation_compactness(self, group, optimal_fit_count: int, optimal_utilization: float) -> bool:
        """
        评估旋转后的紧密度，防止产生大面积空隙

        Args:
            group: 图片分组
            optimal_fit_count: 最优放置数量
            optimal_utilization: 最优利用率

        Returns:
            bool: 紧密度是否可接受
        """
        width_cm, height_cm = group.width_cm, group.height_cm

        # 计算旋转前后的空间利用情况
        original_fit = int(self.canvas_width_cm / width_cm)
        original_utilization = (original_fit * width_cm) / self.canvas_width_cm if original_fit > 0 else 0

        # 计算旋转后的空间利用情况
        rotated_utilization = (optimal_fit_count * height_cm) / self.canvas_width_cm

        # 紧密度评估标准（调整为更宽松的条件）：
        # 1. 旋转后利用率必须显著高于原来（降低阈值到8%，对高利用率场景更宽松）
        utilization_improvement = rotated_utilization - original_utilization
        if rotated_utilization >= 0.90:
            # 高利用率场景（≥90%），只需要5%的提升
            significant_improvement = utilization_improvement >= 0.05
        else:
            # 普通场景，需要8%的提升
            significant_improvement = utilization_improvement >= 0.08

        # 2. 旋转后的利用率必须足够高（降低到80%）
        high_utilization = rotated_utilization >= 0.80

        # 3. 避免过度旋转小图片（可能导致排列松散）
        avoid_small_image_rotation = not (width_cm < 30 and height_cm < 30)

        # 4. 检查是否会产生过多的垂直空隙（对典型场景更宽松）
        vertical_gap_acceptable = self._check_vertical_gap_impact(group, optimal_fit_count)

        # 5. 特殊场景检查（如果是特殊场景，跳过部分严格检查）
        is_special_case = self._is_typical_optimization_case(width_cm, height_cm, optimal_utilization)

        if is_special_case:
            # 特殊场景：只要利用率提升且最终利用率合理即可
            compactness_ok = (
                utilization_improvement >= 0.05 and  # 至少5%提升
                rotated_utilization >= 0.85          # 最终利用率至少85%
            )
        else:
            # 普通场景：需要满足所有条件
            compactness_ok = (
                significant_improvement and
                high_utilization and
                avoid_small_image_rotation and
                vertical_gap_acceptable
            )

        if not compactness_ok:
            reasons = []
            if not significant_improvement:
                reasons.append(f"改善不足({utilization_improvement:.1%}<10%)")
            if not high_utilization:
                reasons.append(f"利用率偏低({rotated_utilization:.1%}<85%)")
            if not avoid_small_image_rotation:
                reasons.append(f"小图片旋转风险({width_cm}x{height_cm}cm)")
            if not vertical_gap_acceptable:
                reasons.append("垂直空隙风险")

            self.emit_log(f"🚫 紧密度不足: {width_cm}x{height_cm}cm ({', '.join(reasons)})")

        return compactness_ok

    def _is_typical_optimization_case(self, width_cm: float, height_cm: float, optimal_utilization: float) -> bool:
        """
        基于十位数策略的智能优化场景检测

        核心思想：
        1. 计算画布的目标十位数：(画布宽度-10)/10
        2. 分析图片较小边长的十位数
        3. 检测多少张相同十位数的图片能达到目标利用率

        Args:
            width_cm: 图片宽度
            height_cm: 图片高度
            optimal_utilization: 最优利用率

        Returns:
            bool: 是否是智能优化场景
        """
        # 计算目标十位数
        target_tens_digit = int((self.canvas_width_cm - 10) / 10)

        # 获取图片的较小边长（旋转后会变成宽度）
        smaller_edge = min(width_cm, height_cm)
        larger_edge = max(width_cm, height_cm)

        # 只对横向图片进行优化（宽度 > 高度）
        if width_cm <= height_cm:
            return False

        # 计算较小边长的十位数
        tens_digit = int(smaller_edge // 10)

        # 检查十位数组合策略
        optimization_detected = self._check_tens_digit_optimization(
            tens_digit, target_tens_digit, smaller_edge, optimal_utilization
        )

        if optimization_detected:
            fit_count = target_tens_digit // tens_digit if tens_digit > 0 else 0
            self.emit_log(f"🎯 十位数优化检测: {width_cm}x{height_cm}cm")
            self.emit_log(f"   - 画布目标十位数: {target_tens_digit}")
            self.emit_log(f"   - 图片十位数: {tens_digit} (边长{smaller_edge}cm)")
            self.emit_log(f"   - 可放置数量: {fit_count}张")
            self.emit_log(f"   - 预期利用率: {optimal_utilization:.1%}")

        return optimization_detected

    def _check_tens_digit_optimization(self, tens_digit: int, target_tens_digit: int,
                                     smaller_edge: float, optimal_utilization: float) -> bool:
        """
        检查基于十位数的优化策略

        Args:
            tens_digit: 图片较小边长的十位数
            target_tens_digit: 画布目标十位数
            smaller_edge: 图片较小边长
            optimal_utilization: 最优利用率

        Returns:
            bool: 是否符合十位数优化策略
        """
        if tens_digit <= 0:
            return False

        # 策略1：整数倍关系
        if target_tens_digit % tens_digit == 0:
            fit_count = target_tens_digit // tens_digit
            if fit_count >= 2 and optimal_utilization >= 0.85:
                return True

        # 策略2：组合关系（例如：6+9=15, 8+7=15等）
        # 检查是否存在其他十位数能与当前十位数组合达到目标
        for other_tens in range(1, 10):
            if other_tens != tens_digit:
                # 检查各种组合可能性
                combinations = [
                    tens_digit + other_tens,  # 两种十位数各一张
                    tens_digit * 2 + other_tens,  # 当前十位数两张+其他一张
                    tens_digit + other_tens * 2,  # 当前十位数一张+其他两张
                ]

                for combo in combinations:
                    if combo == target_tens_digit and optimal_utilization >= 0.80:
                        return True

        # 策略3：近似匹配（允许小幅偏差）
        if tens_digit > 0:
            fit_count = target_tens_digit // tens_digit
            actual_utilization = (fit_count * smaller_edge) / self.canvas_width_cm

            # 如果实际利用率很高，即使不是完美匹配也接受
            if fit_count >= 2 and actual_utilization >= 0.88:
                return True

        return False

    def _check_vertical_gap_impact(self, group, optimal_fit_count: int) -> bool:
        """
        检查旋转后是否会产生过多的垂直空隙（对典型场景更宽松）

        Args:
            group: 图片分组
            optimal_fit_count: 最优放置数量

        Returns:
            bool: 垂直空隙是否可接受
        """
        width_cm, height_cm = group.width_cm, group.height_cm

        # 旋转后，原来的宽度变成高度
        rotated_height = width_cm

        # 对于典型优化场景，使用更宽松的标准
        is_typical = self._is_typical_optimization_case(width_cm, height_cm, 0.85)  # 临时利用率

        if is_typical:
            # 典型场景：更宽松的高度限制
            max_reasonable_height = self.canvas_width_cm * 0.8  # 允许高度达到画布宽度的80%
            estimated_canvas_height = self.canvas_width_cm * 3.0  # 假设画布高度可以很大
            min_rows = 2  # 至少能放2行即可
        else:
            # 普通场景：原有的严格标准
            max_reasonable_height = self.canvas_width_cm / 3
            estimated_canvas_height = self.canvas_width_cm * 1.5
            min_rows = 3

        # 检查旋转后的高度是否合理
        height_reasonable = rotated_height <= max_reasonable_height

        # 检查是否会导致行数过少
        estimated_rows = int(estimated_canvas_height / rotated_height) if rotated_height > 0 else 0
        sufficient_rows = estimated_rows >= min_rows

        if not height_reasonable or not sufficient_rows:
            reason = []
            if not height_reasonable:
                reason.append(f"高度过大({rotated_height:.1f}cm>{max_reasonable_height:.1f}cm)")
            if not sufficient_rows:
                reason.append(f"行数不足({estimated_rows}<{min_rows})")

            # 只对非典型场景记录警告
            if not is_typical:
                self.emit_log(f"⚠️ 垂直空隙风险: {width_cm}x{height_cm}cm ({', '.join(reason)})")

        return height_reasonable and sufficient_rows

    def _check_special_case_optimization(self, group, optimal_fit_count: int, optimal_utilization: float) -> bool:
        """
        基于十位数策略的智能优化检测（替代硬编码的特殊场景）

        使用通用的十位数算法检测所有可能的优化场景
        """
        width_cm, height_cm = group.width_cm, group.height_cm

        # 使用十位数策略进行通用检测
        is_optimization_case = self._is_typical_optimization_case(width_cm, height_cm, optimal_utilization)

        if is_optimization_case:
            # 额外验证：确保有足够的图片数量和合理的利用率
            if (optimal_fit_count >= 2 and
                optimal_utilization >= 0.85 and
                group.size() >= optimal_fit_count):

                # 计算十位数信息用于日志
                target_tens_digit = int((self.canvas_width_cm - 10) / 10)
                smaller_edge = min(width_cm, height_cm)
                tens_digit = int(smaller_edge // 10)

                self.emit_log(f"🎯 十位数优化场景: {width_cm}x{height_cm}cm")
                self.emit_log(f"   - 画布{self.canvas_width_cm}cm → 目标十位数{target_tens_digit}")
                self.emit_log(f"   - 图片边长{smaller_edge}cm → 十位数{tens_digit}")
                self.emit_log(f"   - 旋转后{optimal_fit_count}张/行，利用率{optimal_utilization:.1%}")
                return True

        # 保留一个基本的横向图片检测作为后备
        if (width_cm > height_cm * 1.8 and  # 明显的横向图片
            optimal_fit_count >= 2 and      # 旋转后至少能放2张
            optimal_utilization >= 0.88):   # 利用率至少88%

            self.emit_log(f"🎯 横向图片优化: {width_cm}x{height_cm}cm，"
                        f"旋转后{optimal_fit_count}张/行，利用率{optimal_utilization:.1%}")
            return True

        return False

    def analyze_tens_digit_opportunities(self, pattern_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析所有图片的十位数优化机会

        Args:
            pattern_items: 图片列表

        Returns:
            Dict[str, Any]: 十位数分析结果
        """
        target_tens_digit = int((self.canvas_width_cm - 10) / 10)

        # 按十位数分组统计
        tens_digit_groups = {}

        for pattern in pattern_items:
            width_cm = pattern.get('width_cm', 0)
            height_cm = pattern.get('height_cm', 0)

            # 只分析横向图片
            if width_cm > height_cm:
                smaller_edge = height_cm
                tens_digit = int(smaller_edge // 10)

                if tens_digit not in tens_digit_groups:
                    tens_digit_groups[tens_digit] = {
                        'count': 0,
                        'examples': [],
                        'potential_fit': 0,
                        'potential_utilization': 0.0
                    }

                tens_digit_groups[tens_digit]['count'] += 1
                if len(tens_digit_groups[tens_digit]['examples']) < 3:
                    tens_digit_groups[tens_digit]['examples'].append(f"{width_cm}x{height_cm}")

                # 计算潜在的放置数量和利用率
                if tens_digit > 0:
                    fit_count = target_tens_digit // tens_digit
                    utilization = (fit_count * smaller_edge) / self.canvas_width_cm
                    tens_digit_groups[tens_digit]['potential_fit'] = fit_count
                    tens_digit_groups[tens_digit]['potential_utilization'] = utilization

        # 分析优化机会
        optimization_opportunities = []

        for tens_digit, group_info in tens_digit_groups.items():
            if tens_digit > 0 and group_info['count'] >= 2:
                fit_count = group_info['potential_fit']
                utilization = group_info['potential_utilization']

                if fit_count >= 2 and utilization >= 0.80:
                    opportunity = {
                        'tens_digit': tens_digit,
                        'image_count': group_info['count'],
                        'fit_count': fit_count,
                        'utilization': utilization,
                        'examples': group_info['examples'],
                        'optimization_score': utilization * fit_count * group_info['count']
                    }
                    optimization_opportunities.append(opportunity)

        # 按优化分数排序
        optimization_opportunities.sort(key=lambda x: x['optimization_score'], reverse=True)

        # 输出分析结果
        self.emit_log(f"🔍 十位数优化机会分析 (画布{self.canvas_width_cm}cm → 目标十位数{target_tens_digit}):")

        if optimization_opportunities:
            for i, opp in enumerate(optimization_opportunities[:5]):  # 显示前5个机会
                self.emit_log(f"   {i+1}. 十位数{opp['tens_digit']}: {opp['image_count']}张图片")
                self.emit_log(f"      - 可放置: {opp['fit_count']}张/行")
                self.emit_log(f"      - 利用率: {opp['utilization']:.1%}")
                self.emit_log(f"      - 示例: {', '.join(opp['examples'])}")
                self.emit_log(f"      - 优化分数: {opp['optimization_score']:.1f}")
        else:
            self.emit_log("   未发现明显的十位数优化机会")

        return {
            'target_tens_digit': target_tens_digit,
            'tens_digit_groups': tens_digit_groups,
            'optimization_opportunities': optimization_opportunities
        }

    def _apply_optimization_strategies(self, pattern_items: List[Dict[str, Any]],
                                     optimized_groups: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """应用优化策略到图片列表"""
        if not optimized_groups:
            return pattern_items
            
        # 创建优化策略映射
        optimization_map = {}
        for opt_result in optimized_groups:
            group = opt_result['group']
            size_key = (round(group.width_cm, 1), round(group.height_cm, 1))
            optimization_map[size_key] = opt_result
        
        # 应用优化策略
        optimized_items = []
        for pattern in pattern_items:
            width_cm = pattern.get('width_cm', 0)
            height_cm = pattern.get('height_cm', 0)
            size_key = (round(width_cm, 1), round(height_cm, 1))
            
            # 复制原始图片信息
            optimized_pattern = pattern.copy()
            
            # 如果有优化策略，应用它
            if size_key in optimization_map:
                opt_result = optimization_map[size_key]
                if opt_result['optimal_rotation']:
                    # 标记需要旋转
                    optimized_pattern['intelligent_rotation_suggested'] = True
                    optimized_pattern['optimization_reason'] = f"智能分组优化: 利用率{opt_result['optimal_utilization']:.1%}"
                    optimized_pattern['group_fit_count'] = opt_result['optimal_fit_count']
                else:
                    optimized_pattern['intelligent_rotation_suggested'] = False
                    optimized_pattern['optimization_reason'] = f"保持原方向: 利用率{opt_result['optimal_utilization']:.1%}"
                    
                # 添加分组优先级（用于排序）
                optimized_pattern['grouping_priority'] = opt_result['optimal_utilization']
            else:
                # 没有特殊优化策略的图片
                optimized_pattern['grouping_priority'] = 0.0
                
            optimized_items.append(optimized_pattern)
        
        # 按分组优先级排序，优化的分组优先排列
        optimized_items.sort(key=lambda x: x.get('grouping_priority', 0), reverse=True)
        
        return optimized_items
    
    def _log_optimization_summary(self, optimized_groups: List[Dict[str, Any]], 
                                 total_optimized_images: int, total_images: int):
        """输出优化结果摘要"""
        if not optimized_groups:
            self.emit_log("📋 智能分组优化完成: 未找到可优化的分组")
            return
            
        self.emit_log("📋 智能分组优化完成:")
        self.emit_log(f"   - 优化分组数: {len(optimized_groups)}")
        self.emit_log(f"   - 优化图片数: {total_optimized_images}/{total_images}")
        self.emit_log(f"   - 优化比例: {total_optimized_images/total_images:.1%}")
        
        # 显示各分组的优化效果
        for i, opt_result in enumerate(optimized_groups):
            group = opt_result['group']
            self.emit_log(f"   - 分组{i+1}: {group.width_cm}x{group.height_cm}cm")
            self.emit_log(f"     * 图片数量: {group.size()}")
            self.emit_log(f"     * 优化策略: {opt_result['strategy_desc']}")
            self.emit_log(f"     * 横向利用率: {opt_result['optimal_utilization']:.1%}")
            
        # 计算预期的整体利用率提升
        avg_utilization = sum(opt['optimal_utilization'] for opt in optimized_groups) / len(optimized_groups)
        self.emit_log(f"🎯 预期平均横向利用率: {avg_utilization:.1%}")
        
        if avg_utilization >= self.target_utilization:
            self.emit_log(f"🎉 达到目标利用率 {self.target_utilization:.1%}！")
        else:
            self.emit_log(f"⚠️ 未达到目标利用率 {self.target_utilization:.1%}，但已尽力优化")
