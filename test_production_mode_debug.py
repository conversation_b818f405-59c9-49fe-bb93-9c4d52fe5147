#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生产模式调试脚本
验证实际生产环境中的测试模式设置和智能分组优化器的工作状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.unified_image_arranger import UnifiedImageArranger
from core.intelligent_grouping_optimizer import IntelligentGroupingOptimizer

def test_production_mode_settings():
    """测试不同测试模式设置下的智能分组优化器行为"""
    print("=" * 80)
    print("生产模式设置调试测试")
    print("=" * 80)
    
    # 创建测试数据（基于用户报告的137x51cm图片）
    test_images = []
    for i in range(17):
        test_images.append({
            'pattern_name': f'137x51_图片_{i+1}',
            'path': f'137x51_{i+1}.jpg',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'quantity': 1,
            'index': i,
            'row_number': 1
        })
    
    print(f"测试数据：17张 137x51cm 图片")
    print(f"画布宽度：163cm（基础）+ 3cm（拓展）= 166cm")
    
    # 测试不同的is_test_mode设置
    test_scenarios = [
        {"name": "测试模式", "is_test_mode": True, "description": "is_test_mode=True"},
        {"name": "生产模式", "is_test_mode": False, "description": "is_test_mode=False"},
    ]
    
    canvas_width_px = int(166.0 * 72 / 2.54)  # 166cm转换为像素
    max_height_px = int(200.0 * 72 / 2.54)
    
    for scenario in test_scenarios:
        print(f"\n" + "=" * 60)
        print(f"测试场景：{scenario['name']} ({scenario['description']})")
        print("=" * 60)
        
        # 创建UnifiedImageArranger
        arranger = UnifiedImageArranger()
        arranger.initialize(
            canvas_width_px=canvas_width_px,
            max_height_px=max_height_px,
            image_spacing_px=0,
            ppi=72,
            is_test_mode=scenario['is_test_mode']
        )
        
        # 检查智能分组优化器的设置
        if hasattr(arranger, 'grouping_optimizer') and arranger.grouping_optimizer:
            optimizer = arranger.grouping_optimizer
            print(f"\n🔍 智能分组优化器设置检查：")
            print(f"  - production_mode: {getattr(optimizer, 'production_mode', 'N/A')}")
            print(f"  - target_utilization: {getattr(optimizer, 'target_utilization', 'N/A')}")
            print(f"  - min_utilization_threshold: {getattr(optimizer, 'min_utilization_threshold', 'N/A')}")
            print(f"  - min_improvement_ratio: {getattr(optimizer, 'min_improvement_ratio', 'N/A')}")
            
            # 测试优化效果
            print(f"\n🧪 优化测试：")
            optimized_images = optimizer.optimize_image_grouping(test_images)
            
            rotation_suggested = sum(1 for img in optimized_images 
                                   if img.get('intelligent_rotation_suggested', False))
            
            print(f"  - 建议旋转：{rotation_suggested}/{len(optimized_images)} 张")
            print(f"  - 旋转比例：{rotation_suggested/len(optimized_images)*100:.1f}%")
            
            if rotation_suggested > 0:
                print(f"  ✅ 成功识别旋转优化机会")
                # 显示优化原因
                for img in optimized_images:
                    if img.get('intelligent_rotation_suggested', False):
                        reason = img.get('optimization_reason', '')
                        print(f"    - 优化原因：{reason}")
                        break
            else:
                print(f"  ❌ 未识别到旋转优化机会")
                
        else:
            print(f"❌ 智能分组优化器未初始化")

def test_canvas_width_calculation():
    """测试画布宽度计算的影响"""
    print(f"\n" + "=" * 80)
    print("画布宽度计算测试")
    print("=" * 80)
    
    # 创建测试数据
    test_images = []
    for i in range(3):  # 只用3张图片测试
        test_images.append({
            'pattern_name': f'137x51_图片_{i+1}',
            'path': f'137x51_{i+1}.jpg',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'quantity': 1,
            'index': i,
            'row_number': 1
        })
    
    # 测试不同的画布宽度
    canvas_widths = [
        {"name": "基础宽度", "width_cm": 163.0, "description": "用户报告中的基础宽度"},
        {"name": "实际宽度", "width_cm": 166.0, "description": "用户报告中的实际宽度（含拓展）"},
        {"name": "测试宽度", "width_cm": 160.0, "description": "更小的测试宽度"},
    ]
    
    for canvas_config in canvas_widths:
        print(f"\n画布配置：{canvas_config['name']} - {canvas_config['width_cm']}cm")
        print(f"描述：{canvas_config['description']}")
        
        # 直接测试智能分组优化器
        optimizer = IntelligentGroupingOptimizer(
            canvas_width_cm=canvas_config['width_cm'],
            target_utilization=0.93,
            production_mode=True,  # 强制启用生产模式
            log_signal=None
        )
        
        optimized_images = optimizer.optimize_image_grouping(test_images)
        
        rotation_suggested = sum(1 for img in optimized_images 
                               if img.get('intelligent_rotation_suggested', False))
        
        print(f"  - 建议旋转：{rotation_suggested}/{len(optimized_images)} 张")
        
        # 手动计算验证
        original_fit = int(canvas_config['width_cm'] / 137.0)
        original_utilization = (original_fit * 137.0) / canvas_config['width_cm']
        
        rotated_fit = int(canvas_config['width_cm'] / 51.0)
        rotated_utilization = (rotated_fit * 51.0) / canvas_config['width_cm']
        
        print(f"  - 原始方向：{original_fit}张/行，利用率{original_utilization:.1%}")
        print(f"  - 旋转后：{rotated_fit}张/行，利用率{rotated_utilization:.1%}")
        
        if rotated_utilization > original_utilization:
            print(f"  💡 旋转有优势，应该被优化")
        else:
            print(f"  ⚠️ 旋转无优势")

def test_real_production_flow():
    """模拟真实生产流程"""
    print(f"\n" + "=" * 80)
    print("真实生产流程模拟测试")
    print("=" * 80)
    
    # 模拟用户报告1的完整数据
    test_images = []
    
    # 137x51cm图片（17张）
    for i in range(17):
        test_images.append({
            'pattern_name': f'71915174_{i+1}',
            'path': f'71915174_{i+1}.jpg',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'quantity': 1,
            'index': i,
            'row_number': 1
        })
    
    # 59x35cm图片（4张）
    for i in range(4):
        test_images.append({
            'pattern_name': f'71915078_{i+1}',
            'path': f'71915078_{i+1}.jpg',
            'width_cm': 59.0,
            'height_cm': 35.0,
            'quantity': 1,
            'index': 17 + i,
            'row_number': 1
        })
    
    print(f"模拟数据：{len(test_images)} 张图片")
    print(f"- 137x51cm: 17张")
    print(f"- 59x35cm: 4张")
    
    # 模拟实际的画布配置
    canvas_width_cm = 163.0  # 基础宽度
    horizontal_expansion_cm = 3.0  # 水平拓展
    actual_canvas_width_cm = canvas_width_cm + horizontal_expansion_cm  # 166cm
    
    canvas_width_px = int(actual_canvas_width_cm * 72 / 2.54)
    max_height_px = int(500.0 * 72 / 2.54)  # 5000cm的高度限制
    
    print(f"\n画布配置：")
    print(f"- 基础宽度：{canvas_width_cm}cm")
    print(f"- 水平拓展：{horizontal_expansion_cm}cm")
    print(f"- 实际宽度：{actual_canvas_width_cm}cm ({canvas_width_px}px)")
    
    # 测试生产模式
    print(f"\n🏭 生产模式测试：")
    
    arranger = UnifiedImageArranger()
    arranger.initialize(
        canvas_width_px=canvas_width_px,
        max_height_px=max_height_px,
        image_spacing_px=0,
        ppi=72,
        is_test_mode=False  # 生产模式
    )
    
    # 检查智能分组优化器
    if hasattr(arranger, 'grouping_optimizer') and arranger.grouping_optimizer:
        optimizer = arranger.grouping_optimizer
        print(f"智能分组优化器配置：")
        print(f"  - production_mode: {getattr(optimizer, 'production_mode', 'N/A')}")
        print(f"  - canvas_width_cm: {getattr(optimizer, 'canvas_width_cm', 'N/A')}")
        
        # 执行优化
        optimized_images = optimizer.optimize_image_grouping(test_images)
        
        # 统计结果
        total_rotation_suggested = sum(1 for img in optimized_images 
                                     if img.get('intelligent_rotation_suggested', False))
        
        rotation_137x51 = sum(1 for img in optimized_images 
                            if img.get('width_cm') == 137.0 and img.get('height_cm') == 51.0 
                            and img.get('intelligent_rotation_suggested', False))
        
        rotation_59x35 = sum(1 for img in optimized_images 
                           if img.get('width_cm') == 59.0 and img.get('height_cm') == 35.0 
                           and img.get('intelligent_rotation_suggested', False))
        
        print(f"\n优化结果：")
        print(f"  - 总建议旋转：{total_rotation_suggested}/{len(optimized_images)} 张")
        print(f"  - 137x51cm旋转：{rotation_137x51}/17 张")
        print(f"  - 59x35cm旋转：{rotation_59x35}/4 张")
        
        if rotation_137x51 == 17:
            print(f"  ✅ 137x51cm图片全部被建议旋转（符合预期）")
        else:
            print(f"  ❌ 137x51cm图片未全部被建议旋转（不符合预期）")
            
        if rotation_59x35 > 0:
            print(f"  ✅ 59x35cm图片被建议旋转")
        else:
            print(f"  ⚠️ 59x35cm图片未被建议旋转")
    
    else:
        print(f"❌ 智能分组优化器未初始化")

if __name__ == "__main__":
    print("生产模式调试测试")
    print("验证实际生产环境中的智能分组优化器工作状态")
    
    # 测试1：不同测试模式设置
    test_production_mode_settings()
    
    # 测试2：画布宽度计算影响
    test_canvas_width_calculation()
    
    # 测试3：真实生产流程模拟
    test_real_production_flow()
    
    print(f"\n" + "=" * 80)
    print("调试总结")
    print("=" * 80)
    print("如果137x51cm图片在生产模式下仍未被旋转，可能的原因：")
    print("1. 智能分组优化器的production_mode设置不正确")
    print("2. 画布宽度计算与实际不符")
    print("3. 旋转建议没有被正确传递到RectPack排列器")
    print("4. 实际生产环境中有其他因素干扰")
