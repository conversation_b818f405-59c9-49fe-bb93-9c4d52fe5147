#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法TXT文档生成模块
第四步：为生产模式添加TXT文档生成功能，与测试模式保持一致
"""

import logging
import os
from typing import List, Dict, Any

log = logging.getLogger(__name__)

class RectPackTxtGenerator:
    """RectPack算法TXT文档生成器"""

    def __init__(self):
        log.info("RectPack TXT文档生成器初始化完成")

    def generate_production_txt_doc(self,
                                  doc_path: str,
                                  arranged_images: List[Dict[str, Any]],
                                  canvas_width_px: int,
                                  canvas_height_px: int,
                                  material_name: str = "",
                                  canvas_sequence: int = 1,
                                  ppi: int = 72,
                                  horizontal_expansion_cm: float = 0,
                                  image_spacing_cm: float = 0,
                                  max_height_cm: float = 5000) -> bool:
        """
        生成生产模式TXT文档，与测试模式格式保持一致

        Args:
            doc_path: 文档路径
            arranged_images: 排列的图片列表
            canvas_width_px: 画布宽度(px)
            canvas_height_px: 画布高度(px)
            material_name: 材质名称
            canvas_sequence: 画布序号
            ppi: PPI设置
            horizontal_expansion_cm: 水平拓展(cm)
            image_spacing_cm: 图片间距(cm)
            max_height_cm: 最大高度(cm)

        Returns:
            bool: 是否生成成功
        """
        try:
            # 获取当前时间
            from datetime import datetime
            generation_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 计算统计信息
            stats = self._calculate_statistics(arranged_images, canvas_width_px, canvas_height_px)

            # 生成文档内容
            content = self._generate_content(
                arranged_images, canvas_width_px, canvas_height_px,
                material_name, canvas_sequence, ppi, generation_time, stats,
                horizontal_expansion_cm, image_spacing_cm, max_height_cm
            )

            # 写入文件
            with open(doc_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))

            log.info(f"生产模式TXT文档生成成功: {doc_path}")
            return True

        except Exception as e:
            log.error(f"生成生产模式TXT文档失败: {str(e)}")
            return False

    def _calculate_statistics(self, arranged_images: List[Dict[str, Any]],
                            canvas_width_px: int, canvas_height_px: int) -> Dict[str, Any]:
        """计算统计信息"""
        if not arranged_images:
            return {
                'used_area': 0,
                'container_area': canvas_width_px * canvas_height_px,
                'utilization_rate': 0.0,
                'rotation_count': 0,
                'avg_area': 0
            }

        used_area = sum(img.get('width', 0) * img.get('height', 0) for img in arranged_images)
        container_area = canvas_width_px * canvas_height_px
        utilization_rate = (used_area / container_area * 100) if container_area > 0 else 0
        rotation_count = sum(1 for img in arranged_images if img.get('need_rotation', False) or img.get('rotated', False))
        avg_area = used_area / len(arranged_images) if arranged_images else 0

        return {
            'used_area': used_area,
            'container_area': container_area,
            'utilization_rate': utilization_rate,
            'rotation_count': rotation_count,
            'avg_area': avg_area
        }

    def _generate_content(self, arranged_images: List[Dict[str, Any]],
                         canvas_width_px: int, canvas_height_px: int,
                         material_name: str, canvas_sequence: int, ppi: int,
                         generation_time: str, stats: Dict[str, Any],
                         horizontal_expansion_cm: float = 0,
                         image_spacing_cm: float = 0,
                         max_height_cm: float = 5000) -> List[str]:
        """生成文档内容"""
        content = []

        # 标题 - 与用户示例格式一致
        content.append("图像排列算法生产模式报告")
        content.append(f"生成时间: {generation_time}")

        # 基本信息
        content.append("★ 基本信息")
        content.append(f"材质名称: {material_name or '图像排列材质'}")
        content.append(f"画布序号: {canvas_sequence}")
        content.append("算法类型: 图像排列算法")
        content.append("生产模式: 启用 (Photoshop集成)")

        # 容器详情 - 按照用户示例格式
        # 计算基础宽度（不含水平拓展）
        base_width_px = canvas_width_px - int(horizontal_expansion_cm * ppi / 2.54) if horizontal_expansion_cm > 0 else canvas_width_px
        base_width_cm = base_width_px * 2.54 / ppi
        actual_width_cm = canvas_width_px * 2.54 / ppi
        actual_height_cm = canvas_height_px * 2.54 / ppi

        content.append("★ 容器详情")
        content.append(f"基础宽度: {base_width_cm:.0f}cm")
        content.append(f"水平拓展: {horizontal_expansion_cm:.0f}cm")
        content.append(f"实际宽度: {actual_width_cm:.0f}cm")
        content.append(f"最大高度: {max_height_cm:.0f}cm")
        content.append(f"实际高度: {actual_height_cm:.0f}cm")
        content.append(f"图片间距: {image_spacing_cm:.0f}cm")
        content.append("单位转换: cm直接转px (生产模式)")

        # 布局统计 - 按照用户示例格式
        content.append("★ 布局统计")
        content.append(f"成功放置图片: {len(arranged_images)}张")
        content.append(f"已用面积: {stats['used_area']:,}px²")
        content.append(f"画布面积: {stats['container_area']:,}px²")
        content.append(f"利用率: {stats['utilization_rate']:.2f}%")
        content.append(f"旋转图片数: {stats['rotation_count']}张")
        content.append(f"平均图片面积: {stats['avg_area']:.0f}px²")

        # 利用率评价
        utilization_rate = stats['utilization_rate']
        if utilization_rate >= 85:
            rating = "★★★★★ 优秀"
        elif utilization_rate >= 75:
            rating = "★★★★☆ 良好"
        elif utilization_rate >= 65:
            rating = "★★★☆☆ 中等"
        elif utilization_rate >= 50:
            rating = "★★☆☆☆ 较差"
        else:
            rating = "★☆☆☆☆ 待优化"

        content.append(f"利用率评价: {rating}")

        # 详细图片信息 - 按照用户示例格式
        content.append("★ 详细图片信息")
        content.append("| 序号 | 图片标识 | 尺寸(px) | 位置(x,y) | 尺寸(cm) | 旋转 | 面积(px²) |")
        content.append("|------|----------|----------|-----------|----------|------|-----------|")

        for i, img in enumerate(arranged_images, 1):
            name = img.get('name', f'Image_{i}')
            width = img.get('width', 0)
            height = img.get('height', 0)
            x = img.get('x', 0)
            y = img.get('y', 0)
            rotated = '是' if (img.get('need_rotation', False) or img.get('rotated', False)) else '否'
            area = width * height

            # 使用表格中的厘米尺寸数据，避免单位换算误差
            width_cm = img.get('width_cm', img.get('original_width_cm', 0))
            height_cm = img.get('height_cm', img.get('original_height_cm', 0))
            size_cm = f"{width_cm:.1f}x{height_cm:.1f}"

            content.append(f"| {i} | {name} | {width}x{height} | ({x},{y}) | {size_cm} | {rotated} | {area:,} |")

        # 算法参数 - 按照用户示例格式
        content.append("★ 算法参数")
        content.append("排序策略: 按面积排序")
        content.append("装箱算法: 智能排列算法")
        content.append("旋转功能: 启用")
        content.append("间距处理: 统一间距")
        content.append("优化目标: 最大化空间利用率")

        return content

# 全局生成器实例
txt_generator = RectPackTxtGenerator()
