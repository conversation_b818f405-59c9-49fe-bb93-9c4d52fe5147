#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试旋转统计脚本
检查为什么智能旋转建议被应用了，但最终统计显示旋转数为0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.unified_image_arranger import UnifiedImageArranger

def debug_rotation_statistics():
    """调试旋转统计问题"""
    print("=" * 80)
    print("调试旋转统计问题")
    print("=" * 80)
    
    # 创建简单测试数据
    test_images = []
    for i in range(3):
        test_images.append({
            'pattern_name': f'137x51_test_{i+1}',
            'path': f'137x51_test_{i+1}.jpg',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'quantity': 1,
            'index': i,
            'row_number': 1
        })
    
    print(f"测试数据：3张 137x51cm 图片")
    
    # 画布配置
    canvas_width_cm = 166.0
    canvas_width_px = int(canvas_width_cm * 72 / 2.54)
    max_height_px = int(200.0 * 72 / 2.54)
    
    # 生产模式测试
    arranger = UnifiedImageArranger()
    arranger.initialize(
        canvas_width_px=canvas_width_px,
        max_height_px=max_height_px,
        image_spacing_px=0,
        ppi=72,
        is_test_mode=False  # 生产模式
    )
    
    # 执行排列
    arranged_images = arranger.arrange_images(test_images)
    
    print(f"\n📊 排列结果详细分析：")
    print(f"  - 成功排列：{len(arranged_images)} 张")
    
    # 详细检查每张图片的旋转状态
    for i, img in enumerate(arranged_images):
        print(f"\n图片 {i+1}: {img.get('pattern_name', 'Unknown')}")
        print(f"  - 位置：({img.get('x', 0)}, {img.get('y', 0)})")
        print(f"  - 尺寸：{img.get('width', 0)}x{img.get('height', 0)}px")
        print(f"  - 原始尺寸：{img.get('width_cm', 0)}x{img.get('height_cm', 0)}cm")
        print(f"  - need_rotation: {img.get('need_rotation', False)}")
        print(f"  - rotated: {img.get('rotated', False)}")
        print(f"  - intelligent_rotation_suggested: {img.get('intelligent_rotation_suggested', False)}")
        print(f"  - optimization_reason: {img.get('optimization_reason', 'N/A')}")
        
        # 检查尺寸是否符合旋转预期
        width_px = img.get('width', 0)
        height_px = img.get('height', 0)
        width_cm = img.get('width_cm', 0)
        height_cm = img.get('height_cm', 0)
        
        # 原始尺寸：137x51cm -> 3883x1445px
        # 旋转后：51x137cm -> 1445x3883px
        expected_rotated_width = int(51.0 * 72 / 2.54)  # 约1445px
        expected_rotated_height = int(137.0 * 72 / 2.54)  # 约3883px
        
        # 允许1-2像素的误差（由于像素转换精度问题）
        width_diff = abs(width_px - expected_rotated_width)
        height_diff = abs(height_px - expected_rotated_height)
        is_actually_rotated = (width_diff <= 2 and height_diff <= 2)
        
        print(f"  - 实际是否旋转：{is_actually_rotated}")
        print(f"  - 期望旋转尺寸：{expected_rotated_width}x{expected_rotated_height}px")
        
        if img.get('intelligent_rotation_suggested', False) and not img.get('need_rotation', False):
            print(f"  ⚠️ 问题：智能建议旋转但need_rotation为False")
        
        if is_actually_rotated and not img.get('need_rotation', False):
            print(f"  ⚠️ 问题：实际已旋转但need_rotation为False")
    
    # 统计旋转情况
    total_images = len(arranged_images)
    need_rotation_count = sum(1 for img in arranged_images if img.get('need_rotation', False))
    rotated_count = sum(1 for img in arranged_images if img.get('rotated', False))
    intelligent_suggested_count = sum(1 for img in arranged_images if img.get('intelligent_rotation_suggested', False))
    
    print(f"\n📈 旋转统计：")
    print(f"  - 总图片数：{total_images}")
    print(f"  - need_rotation=True：{need_rotation_count} 张")
    print(f"  - rotated=True：{rotated_count} 张")
    print(f"  - intelligent_rotation_suggested=True：{intelligent_suggested_count} 张")
    
    # 手动检查实际旋转情况
    actually_rotated_count = 0
    for img in arranged_images:
        width_px = img.get('width', 0)
        height_px = img.get('height', 0)
        expected_rotated_width = int(51.0 * 72 / 2.54)
        expected_rotated_height = int(137.0 * 72 / 2.54)
        
        # 允许1-2像素的误差（由于像素转换精度问题）
        width_diff = abs(width_px - expected_rotated_width)
        height_diff = abs(height_px - expected_rotated_height)
        if width_diff <= 2 and height_diff <= 2:
            actually_rotated_count += 1
    
    print(f"  - 实际旋转（基于尺寸）：{actually_rotated_count} 张")
    
    # 分析问题
    print(f"\n🔍 问题分析：")
    if intelligent_suggested_count == total_images:
        print(f"  ✅ 智能分组优化器正常工作")
    else:
        print(f"  ❌ 智能分组优化器有问题")
    
    if actually_rotated_count == total_images:
        print(f"  ✅ 图片实际已被旋转")
    else:
        print(f"  ❌ 图片没有被实际旋转")
    
    if need_rotation_count != actually_rotated_count:
        print(f"  ⚠️ 旋转状态记录不正确：need_rotation={need_rotation_count}, 实际旋转={actually_rotated_count}")
    else:
        print(f"  ✅ 旋转状态记录正确")
    
    return {
        'total_images': total_images,
        'need_rotation_count': need_rotation_count,
        'rotated_count': rotated_count,
        'intelligent_suggested_count': intelligent_suggested_count,
        'actually_rotated_count': actually_rotated_count
    }

if __name__ == "__main__":
    print("旋转统计调试工具")
    print("检查智能旋转建议的应用和统计问题")
    
    result = debug_rotation_statistics()
    
    print(f"\n" + "=" * 80)
    print("调试总结")
    print("=" * 80)
    
    if result['actually_rotated_count'] == result['total_images']:
        if result['need_rotation_count'] == result['actually_rotated_count']:
            print("✅ 旋转功能完全正常")
        else:
            print("⚠️ 旋转功能正常，但统计记录有问题")
            print("需要修复旋转状态的记录逻辑")
    else:
        print("❌ 旋转功能有问题")
        print("需要检查旋转应用逻辑")
