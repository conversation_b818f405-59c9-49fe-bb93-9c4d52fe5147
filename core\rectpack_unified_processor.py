#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack统一处理器
整合测试模式和生产模式，统一使用cm单位计算
"""

import logging
import time
import os
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass

log = logging.getLogger(__name__)

@dataclass
class ProcessingConfig:
    """处理配置"""
    canvas_width_cm: float
    max_height_cm: float
    image_spacing_cm: float
    horizontal_expansion_cm: float
    ppi: int
    is_test_mode: bool
    output_directory: str = ""

class RectPackUnifiedProcessor:
    """RectPack统一处理器"""

    def __init__(self, config: ProcessingConfig):
        """
        初始化统一处理器

        Args:
            config: 处理配置
        """
        self.config = config
        self.cm_core = None

        # 初始化CM核心
        self._initialize_cm_core()

        log.info(f"RectPack统一处理器初始化完成，模式: {'测试' if config.is_test_mode else '生产'}")

    def _initialize_cm_core(self):
        """初始化CM核心"""
        try:
            from core.rectpack_cm_core import RectPackCMCore

            # 计算实际画布宽度（包含水平扩展）
            actual_width_cm = self.config.canvas_width_cm + self.config.horizontal_expansion_cm

            self.cm_core = RectPackCMCore(
                canvas_width_cm=actual_width_cm,
                max_height_cm=self.config.max_height_cm
            )

            log.info(f"CM核心初始化成功: {actual_width_cm}cm × {self.config.max_height_cm}cm")

        except Exception as e:
            log.error(f"CM核心初始化失败: {str(e)}")
            raise

    def process_images(self, pattern_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        统一处理图片

        Args:
            pattern_items: 图片数据列表

        Returns:
            Dict: 处理结果
        """
        log.info(f"开始统一处理{len(pattern_items)}张图片")
        start_time = time.time()

        try:
            # 第一步：准备图片数据（cm单位）
            images_data_cm = self._prepare_images_data_cm(pattern_items)

            # 第二步：执行CM单位排列
            canvas_results_cm = self.cm_core.arrange_images_cm(images_data_cm)

            if not canvas_results_cm:
                return {
                    'success': False,
                    'error': '图片排列失败',
                    'processing_time': time.time() - start_time
                }

            # 第三步：根据模式生成结果
            if self.config.is_test_mode:
                result = self._process_test_mode(canvas_results_cm, pattern_items)
            else:
                result = self._process_production_mode(canvas_results_cm, pattern_items)

            # 添加统计信息
            stats = self.cm_core.get_optimization_stats(canvas_results_cm)
            result['stats'] = stats
            result['processing_time'] = time.time() - start_time

            log.info(f"统一处理完成，耗时{result['processing_time']:.2f}秒，"
                    f"利用率{stats['overall_utilization']:.2f}%")

            return result

        except Exception as e:
            log.error(f"统一处理失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }

    def _prepare_images_data_cm(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """准备CM单位图片数据"""
        images_data_cm = []

        for item in pattern_items:
            # 提取尺寸信息（cm单位）
            width_cm = float(item.get('width_cm', item.get('宽度', 0)))
            height_cm = float(item.get('height_cm', item.get('高度', 0)))

            if width_cm <= 0 or height_cm <= 0:
                log.warning(f"跳过无效尺寸的图片: {item}")
                continue

            image_data = {
                'width_cm': width_cm,
                'height_cm': height_cm,
                'name': item.get('name', item.get('图案名称', f"image_{len(images_data_cm)+1}")),
                'path': item.get('path', item.get('图片路径', '')),
                'original_item': item  # 保留原始数据
            }
            images_data_cm.append(image_data)

        log.info(f"准备了{len(images_data_cm)}张有效图片数据")
        return images_data_cm

    def _process_test_mode(self, canvas_results_cm, pattern_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理测试模式"""
        log.info("处理测试模式结果")

        # 生成测试模式可视化数据
        visualization_data = self.cm_core.generate_test_mode_visualization(canvas_results_cm)

        # 生成测试模式画布
        test_canvases = self._generate_test_canvases(visualization_data)

        # 生成文档
        documentation = self._generate_test_documentation(canvas_results_cm, pattern_items)

        return {
            'success': True,
            'mode': 'test',
            'canvases': test_canvases,
            'visualization_data': visualization_data,
            'documentation': documentation,
            'canvas_count': len(canvas_results_cm)
        }

    def _process_production_mode(self, canvas_results_cm, pattern_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理生产模式（使用高精度转换）"""
        log.info("处理生产模式结果（高精度模式）")

        # 使用高精度转换为PX单位用于PS处理
        try:
            px_results = self.cm_core.convert_to_px_for_ps_enhanced(canvas_results_cm, self.config.ppi)
            log.info("使用高精度转换器进行生产模式处理")
        except Exception as e:
            log.warning(f"高精度转换失败，回退到标准转换: {str(e)}")
            px_results = self.cm_core.convert_to_px_for_ps(canvas_results_cm, self.config.ppi)

        # 调用PS处理
        ps_results = self._call_photoshop_processing(px_results, pattern_items)

        # 生成生产模式文档
        documentation = self._generate_production_documentation(canvas_results_cm, pattern_items)

        return {
            'success': ps_results['success'],
            'mode': 'production',
            'ps_results': ps_results,
            'documentation': documentation,
            'canvas_count': len(canvas_results_cm),
            'tiff_files': ps_results.get('tiff_files', [])
        }

    def _generate_test_canvases(self, visualization_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成测试模式画布"""
        test_canvases = []

        for viz_data in visualization_data:
            try:
                # 创建测试画布图片
                canvas_image = self._create_test_canvas_image(viz_data)

                canvas = {
                    'canvas_index': viz_data['canvas_index'],
                    'width_px': viz_data['width_px'],
                    'height_px': viz_data['height_px'],
                    'image_path': canvas_image,
                    'utilization_rate': viz_data['utilization_rate'],
                    'stats': viz_data['stats']
                }
                test_canvases.append(canvas)

            except Exception as e:
                log.error(f"生成测试画布{viz_data['canvas_index']}失败: {str(e)}")

        return test_canvases

    def _create_test_canvas_image(self, viz_data: Dict[str, Any]) -> str:
        """创建测试画布图片"""
        try:
            from PIL import Image, ImageDraw, ImageFont

            # 创建画布
            canvas_width = viz_data['width_px']
            canvas_height = viz_data['height_px']

            # 限制画布大小以避免内存问题
            max_size = 2000
            if canvas_width > max_size or canvas_height > max_size:
                scale = min(max_size / canvas_width, max_size / canvas_height)
                canvas_width = int(canvas_width * scale)
                canvas_height = int(canvas_height * scale)
            else:
                scale = 1.0

            image = Image.new('RGB', (canvas_width, canvas_height), 'white')
            draw = ImageDraw.Draw(image)

            # 绘制颜色块
            for block in viz_data['color_blocks']:
                x = int(block['x'] * scale)
                y = int(block['y'] * scale)
                width = int(block['width'] * scale)
                height = int(block['height'] * scale)

                # 绘制矩形
                draw.rectangle([x, y, x + width, y + height],
                             fill=block['color'], outline='black', width=1)

                # 添加文字标签
                try:
                    font = ImageFont.load_default()
                    text = f"{block['image_id']}"
                    if block['rotated']:
                        text += " (R)"

                    # 计算文字位置
                    text_bbox = draw.textbbox((0, 0), text, font=font)
                    text_width = text_bbox[2] - text_bbox[0]
                    text_height = text_bbox[3] - text_bbox[1]

                    text_x = x + (width - text_width) // 2
                    text_y = y + (height - text_height) // 2

                    if text_x >= 0 and text_y >= 0:
                        draw.text((text_x, text_y), text, fill='black', font=font)

                except Exception as e:
                    log.warning(f"绘制文字失败: {str(e)}")

            # 保存图片
            output_dir = self.config.output_directory or "output"
            os.makedirs(output_dir, exist_ok=True)

            canvas_filename = f"test_canvas_{viz_data['canvas_index']}.png"
            canvas_path = os.path.join(output_dir, canvas_filename)

            image.save(canvas_path, 'PNG')
            log.info(f"测试画布图片已保存: {canvas_path}")

            return canvas_path

        except Exception as e:
            log.error(f"创建测试画布图片失败: {str(e)}")
            return ""

    def _call_photoshop_processing(self, px_results: List[Dict[str, Any]],
                                  pattern_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """调用Photoshop处理"""
        log.info("开始调用Photoshop处理")

        try:
            from utils.photoshop_helper import PhotoshopHelper

            # 检查是否使用高精度模式
            use_precision_mode = any(canvas.get('precision_enhanced', False) for canvas in px_results)
            error_processor = None  # 初始化变量

            if use_precision_mode:
                log.info("检测到高精度数据，启用误差补偿模式")
                try:
                    from core.rectpack_high_precision_converter import ErrorCompensatedPSProcessor
                    error_processor = ErrorCompensatedPSProcessor()
                    log.info("误差补偿处理器初始化成功")
                except ImportError:
                    log.warning("误差补偿处理器不可用，使用标准模式")
                    error_processor = None
                except Exception as e:
                    log.error(f"误差补偿处理器初始化失败: {str(e)}")
                    error_processor = None
            else:
                log.info("使用标准模式，未检测到高精度数据")

            tiff_files = []

            for canvas_data in px_results:
                canvas_index = canvas_data['canvas_index']
                canvas_width_px = canvas_data['width_px']
                canvas_height_px = canvas_data['height_px']

                log.info(f"处理画布{canvas_index}: {canvas_width_px}×{canvas_height_px}px")

                # 创建PS画布
                success = PhotoshopHelper.create_canvas(
                    width=canvas_width_px,
                    height=canvas_height_px,
                    ppi=self.config.ppi,
                    name=f"RectPack_Canvas_{canvas_index}"
                )

                if not success:
                    log.error(f"创建PS画布{canvas_index}失败")
                    continue

                # 放置图片（支持误差补偿）
                placement_success = True

                # 如果使用误差补偿模式，重置误差状态
                if error_processor:
                    error_processor.reset_error_compensation()

                for i, placement in enumerate(canvas_data['placements']):
                    # 查找对应的图片路径
                    image_path = self._find_image_path(placement['image_id'], pattern_items)

                    if not image_path or not os.path.exists(image_path):
                        log.warning(f"图片路径不存在: {image_path}")
                        continue

                    # 选择放置方式
                    if error_processor and 'x_precise' in placement:
                        # 使用误差补偿放置
                        place_success = error_processor.place_image_with_compensation(
                            placement_precise=placement,
                            image_path=image_path,
                            rotation=90 if placement['rotated'] else 0,
                            ppi=self.config.ppi,
                            layer_index=i,
                            total_images=len(canvas_data['placements']),
                            layer_name=f"Image_{placement['image_id']}",
                            unique_id=placement['image_id']
                        )
                    else:
                        # 使用标准放置
                        place_success = PhotoshopHelper.place_image(
                            image_path=image_path,
                            x=placement['x'],
                            y=placement['y'],
                            width=placement['width'],
                            height=placement['height'],
                            rotate=placement['rotated'],
                            rotation=90 if placement['rotated'] else 0,
                            ppi=self.config.ppi,
                            layer_index=i,
                            total_images=len(canvas_data['placements']),
                            layer_name=f"Image_{placement['image_id']}",
                            unique_id=placement['image_id']
                        )

                    if not place_success:
                        log.warning(f"放置图片失败: {image_path}")
                        placement_success = False

                # 保存TIFF文件
                if placement_success:
                    output_dir = self.config.output_directory or "output"
                    os.makedirs(output_dir, exist_ok=True)

                    tiff_filename = f"rectpack_canvas_{canvas_index}.tiff"
                    tiff_path = os.path.join(output_dir, tiff_filename)

                    save_success = PhotoshopHelper.save_document(tiff_path, 'TIFF')

                    if save_success:
                        tiff_files.append(tiff_path)
                        log.info(f"TIFF文件已保存: {tiff_path}")
                    else:
                        log.error(f"保存TIFF文件失败: {tiff_path}")

                # 关闭当前文档
                PhotoshopHelper.close_document(save=False)

            return {
                'success': len(tiff_files) > 0,
                'tiff_files': tiff_files,
                'processed_canvases': len(tiff_files),
                'total_canvases': len(px_results)
            }

        except Exception as e:
            log.error(f"Photoshop处理失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'tiff_files': [],
                'processed_canvases': 0,
                'total_canvases': len(px_results)
            }

    def _find_image_path(self, image_id: str, pattern_items: List[Dict[str, Any]]) -> str:
        """查找图片路径"""
        try:
            # image_id是从1开始的索引
            index = int(image_id) - 1
            if 0 <= index < len(pattern_items):
                item = pattern_items[index]
                return item.get('path', item.get('图片路径', ''))
        except (ValueError, IndexError):
            pass

        return ""

    def _generate_test_documentation(self, canvas_results_cm, pattern_items: List[Dict[str, Any]]) -> str:
        """生成测试模式文档"""
        doc_lines = [
            "RectPack算法测试模式排列报告",
            "=" * 50,
            f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"算法模式: 测试模式（使用色块模拟）",
            f"画布规格: {self.config.canvas_width_cm}cm × {self.config.max_height_cm}cm",
            f"图片间距: {self.config.image_spacing_cm}cm",
            f"水平扩展: {self.config.horizontal_expansion_cm}cm",
            ""
        ]

        # 统计信息
        stats = self.cm_core.get_optimization_stats(canvas_results_cm)
        doc_lines.extend([
            "整体统计:",
            f"- 总画布数: {stats['total_canvases']}",
            f"- 总图片数: {stats['total_images']}",
            f"- 整体利用率: {stats['overall_utilization']:.2f}%",
            f"- 平均利用率: {stats['average_utilization']:.2f}%",
            f"- 最高利用率: {stats['best_canvas_utilization']:.2f}%",
            f"- 最低利用率: {stats['worst_canvas_utilization']:.2f}%",
            ""
        ])

        # 各画布详情
        doc_lines.append("各画布详情:")
        for canvas in canvas_results_cm:
            doc_lines.extend([
                f"画布 {canvas.canvas_index + 1}:",
                f"  - 尺寸: {canvas.width_cm:.1f}cm × {canvas.height_cm:.1f}cm",
                f"  - 图片数量: {len(canvas.placements)}",
                f"  - 利用率: {canvas.utilization_rate:.2f}%",
                f"  - 使用面积: {canvas.area_used_cm2:.1f}cm²",
                ""
            ])

        return "\n".join(doc_lines)

    def _generate_production_documentation(self, canvas_results_cm, pattern_items: List[Dict[str, Any]]) -> str:
        """生成生产模式文档"""
        doc_lines = [
            "RectPack算法生产模式排列报告",
            "=" * 50,
            f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"算法模式: 生产模式（Photoshop处理）",
            f"画布规格: {self.config.canvas_width_cm}cm × {self.config.max_height_cm}cm",
            f"分辨率: {self.config.ppi} PPI",
            f"图片间距: {self.config.image_spacing_cm}cm",
            f"水平扩展: {self.config.horizontal_expansion_cm}cm",
            ""
        ]

        # 统计信息
        stats = self.cm_core.get_optimization_stats(canvas_results_cm)
        doc_lines.extend([
            "整体统计:",
            f"- 总画布数: {stats['total_canvases']}",
            f"- 总图片数: {stats['total_images']}",
            f"- 整体利用率: {stats['overall_utilization']:.2f}%",
            f"- 平均利用率: {stats['average_utilization']:.2f}%",
            f"- 最高利用率: {stats['best_canvas_utilization']:.2f}%",
            f"- 最低利用率: {stats['worst_canvas_utilization']:.2f}%",
            ""
        ])

        # 各画布详情
        doc_lines.append("各画布详情:")
        for canvas in canvas_results_cm:
            doc_lines.extend([
                f"画布 {canvas.canvas_index + 1}:",
                f"  - 尺寸: {canvas.width_cm:.1f}cm × {canvas.height_cm:.1f}cm",
                f"  - 像素尺寸: {int(canvas.width_cm * self.config.ppi / 2.54)}px × {int(canvas.height_cm * self.config.ppi / 2.54)}px",
                f"  - 图片数量: {len(canvas.placements)}",
                f"  - 利用率: {canvas.utilization_rate:.2f}%",
                f"  - 使用面积: {canvas.area_used_cm2:.1f}cm²",
                ""
            ])

            # 图片排列详情
            doc_lines.append("  图片排列:")
            for placement in canvas.placements:
                doc_lines.append(
                    f"    - 图片{placement.image_id}: "
                    f"位置({placement.x_cm:.1f}, {placement.y_cm:.1f})cm, "
                    f"尺寸{placement.width_cm:.1f}×{placement.height_cm:.1f}cm"
                    f"{' (旋转)' if placement.rotated else ''}"
                )
            doc_lines.append("")

        return "\n".join(doc_lines)


def main():
    """测试函数"""
    # 测试配置
    config = ProcessingConfig(
        canvas_width_cm=163.0,
        max_height_cm=5000.0,
        image_spacing_cm=1.0,
        horizontal_expansion_cm=0.0,
        ppi=72,
        is_test_mode=True,
        output_directory="test_output"
    )

    # 测试数据
    test_pattern_items = [
        {'width_cm': 190, 'height_cm': 82, 'name': 'image_1', 'path': 'test1.jpg'},
        {'width_cm': 163, 'height_cm': 123, 'name': 'image_2', 'path': 'test2.jpg'},
        {'width_cm': 137, 'height_cm': 51, 'name': 'image_3', 'path': 'test3.jpg'},
        {'width_cm': 123, 'height_cm': 52, 'name': 'image_4', 'path': 'test4.jpg'},
        {'width_cm': 92, 'height_cm': 52, 'name': 'image_5', 'path': 'test5.jpg'}
    ]

    # 创建处理器
    processor = RectPackUnifiedProcessor(config)

    # 执行处理
    result = processor.process_images(test_pattern_items)

    print("RectPack统一处理器测试结果:")
    print(f"成功: {result['success']}")
    print(f"模式: {result.get('mode', 'unknown')}")
    print(f"画布数量: {result.get('canvas_count', 0)}")
    if 'stats' in result:
        stats = result['stats']
        print(f"整体利用率: {stats['overall_utilization']:.2f}%")
        print(f"处理时间: {result.get('processing_time', 0):.2f}秒")


if __name__ == "__main__":
    main()
