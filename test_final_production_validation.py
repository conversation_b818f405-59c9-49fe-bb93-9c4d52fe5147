#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
最终生产模式验证测试
验证用户报告中的具体场景是否都能被正确处理
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.unified_image_arranger import UnifiedImageArranger

def test_user_report_scenario_1():
    """测试用户报告1：137x51cm图片场景"""
    print("=" * 80)
    print("用户报告1验证：137x51cm图片场景")
    print("=" * 80)
    
    # 模拟用户报告1的完整数据
    test_images = []
    
    # 137x51cm图片（17张）
    for i in range(17):
        test_images.append({
            'pattern_name': f'71915174_{i+1}',
            'path': f'71915174_{i+1}.jpg',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'quantity': 1,
            'index': i,
            'row_number': 1
        })
    
    # 59x35cm图片（4张）
    for i in range(4):
        test_images.append({
            'pattern_name': f'71915078_{i+1}',
            'path': f'71915078_{i+1}.jpg',
            'width_cm': 59.0,
            'height_cm': 35.0,
            'quantity': 1,
            'index': 17 + i,
            'row_number': 1
        })
    
    print(f"测试数据：{len(test_images)} 张图片")
    print(f"- 137x51cm: 17张")
    print(f"- 59x35cm: 4张")
    
    # 画布配置（基于用户报告）
    canvas_width_cm = 163.0 + 3.0  # 基础宽度 + 水平拓展
    canvas_width_px = int(canvas_width_cm * 72 / 2.54)
    max_height_px = int(500.0 * 72 / 2.54)
    
    print(f"画布配置：{canvas_width_cm}cm ({canvas_width_px}px)")
    
    # 生产模式测试
    arranger = UnifiedImageArranger()
    arranger.initialize(
        canvas_width_px=canvas_width_px,
        max_height_px=max_height_px,
        image_spacing_px=0,
        ppi=72,
        is_test_mode=False  # 生产模式
    )
    
    # 执行排列
    arranged_images = arranger.arrange_images(test_images)
    stats = arranger.get_layout_statistics()
    
    # 分析结果
    total_placed = len(arranged_images)
    rotation_count_137x51 = sum(1 for img in arranged_images 
                               if img.get('width_cm') == 137.0 and img.get('height_cm') == 51.0 
                               and img.get('need_rotation', False))
    rotation_count_59x35 = sum(1 for img in arranged_images 
                              if img.get('width_cm') == 59.0 and img.get('height_cm') == 35.0 
                              and img.get('need_rotation', False))
    
    print(f"\n📊 排列结果：")
    print(f"  - 成功排列：{total_placed}/{len(test_images)} 张")
    print(f"  - 画布利用率：{stats['utilization_percent']:.2f}%")
    print(f"  - 137x51cm旋转：{rotation_count_137x51}/17 张")
    print(f"  - 59x35cm旋转：{rotation_count_59x35}/4 张")
    
    # 验证期望结果
    expected_137x51_rotated = 17  # 期望所有137x51cm图片都被旋转
    success_137x51 = rotation_count_137x51 == expected_137x51_rotated
    
    print(f"\n✅ 验证结果：")
    if success_137x51:
        print(f"  ✅ 137x51cm图片旋转：{rotation_count_137x51}/17 张 - 完美！")
    else:
        print(f"  ❌ 137x51cm图片旋转：{rotation_count_137x51}/17 张 - 需要改进")
    
    if stats['utilization_percent'] >= 90.0:
        print(f"  ✅ 利用率：{stats['utilization_percent']:.2f}% - 优秀！")
    else:
        print(f"  ⚠️ 利用率：{stats['utilization_percent']:.2f}% - 可以更好")
    
    return {
        'scenario': '用户报告1',
        'total_placed': total_placed,
        'total_images': len(test_images),
        'utilization': stats['utilization_percent'],
        'rotation_137x51': rotation_count_137x51,
        'rotation_59x35': rotation_count_59x35,
        'success': success_137x51 and stats['utilization_percent'] >= 90.0
    }

def test_user_report_scenario_2():
    """测试用户报告2：67x52cm图片场景"""
    print(f"\n" + "=" * 80)
    print("用户报告2验证：67x52cm图片场景")
    print("=" * 80)
    
    # 模拟用户报告2的部分数据（67x52cm图片）
    test_images = []
    
    # 67x52cm图片（30张）
    for i in range(30):
        test_images.append({
            'pattern_name': f'71915175_{i+1}',
            'path': f'71915175_{i+1}.jpg',
            'width_cm': 67.0,
            'height_cm': 52.0,
            'quantity': 1,
            'index': i,
            'row_number': 1
        })
    
    print(f"测试数据：{len(test_images)} 张 67x52cm 图片")
    
    # 画布配置
    canvas_width_cm = 166.0  # 假设与报告1相同
    canvas_width_px = int(canvas_width_cm * 72 / 2.54)
    max_height_px = int(500.0 * 72 / 2.54)
    
    print(f"画布配置：{canvas_width_cm}cm ({canvas_width_px}px)")
    
    # 生产模式测试
    arranger = UnifiedImageArranger()
    arranger.initialize(
        canvas_width_px=canvas_width_px,
        max_height_px=max_height_px,
        image_spacing_px=0,
        ppi=72,
        is_test_mode=False  # 生产模式
    )
    
    # 执行排列
    arranged_images = arranger.arrange_images(test_images)
    stats = arranger.get_layout_statistics()
    
    # 分析结果
    total_placed = len(arranged_images)
    rotation_count = sum(1 for img in arranged_images if img.get('need_rotation', False))
    
    print(f"\n📊 排列结果：")
    print(f"  - 成功排列：{total_placed}/{len(test_images)} 张")
    print(f"  - 画布利用率：{stats['utilization_percent']:.2f}%")
    print(f"  - 旋转图片：{rotation_count}/{total_placed} 张")
    
    # 验证期望结果
    expected_rotated = 30  # 期望所有67x52cm图片都被旋转
    success = rotation_count == expected_rotated
    
    print(f"\n✅ 验证结果：")
    if success:
        print(f"  ✅ 67x52cm图片旋转：{rotation_count}/30 张 - 完美！")
    else:
        print(f"  ❌ 67x52cm图片旋转：{rotation_count}/30 张 - 需要改进")
    
    if stats['utilization_percent'] >= 90.0:
        print(f"  ✅ 利用率：{stats['utilization_percent']:.2f}% - 优秀！")
    else:
        print(f"  ⚠️ 利用率：{stats['utilization_percent']:.2f}% - 可以更好")
    
    return {
        'scenario': '用户报告2',
        'total_placed': total_placed,
        'total_images': len(test_images),
        'utilization': stats['utilization_percent'],
        'rotation_count': rotation_count,
        'success': success and stats['utilization_percent'] >= 90.0
    }

def test_mixed_scenarios():
    """测试混合场景"""
    print(f"\n" + "=" * 80)
    print("混合场景验证：多种尺寸图片")
    print("=" * 80)
    
    # 创建混合测试数据
    test_images = []
    
    # 137x51cm图片（5张）
    for i in range(5):
        test_images.append({
            'pattern_name': f'137x51_{i+1}',
            'path': f'137x51_{i+1}.jpg',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'quantity': 1,
            'index': len(test_images),
            'row_number': 1
        })
    
    # 67x52cm图片（8张）
    for i in range(8):
        test_images.append({
            'pattern_name': f'67x52_{i+1}',
            'path': f'67x52_{i+1}.jpg',
            'width_cm': 67.0,
            'height_cm': 52.0,
            'quantity': 1,
            'index': len(test_images),
            'row_number': 1
        })
    
    # 120x45cm图片（3张）
    for i in range(3):
        test_images.append({
            'pattern_name': f'120x45_{i+1}',
            'path': f'120x45_{i+1}.jpg',
            'width_cm': 120.0,
            'height_cm': 45.0,
            'quantity': 1,
            'index': len(test_images),
            'row_number': 1
        })
    
    print(f"测试数据：{len(test_images)} 张图片")
    print(f"- 137x51cm: 5张")
    print(f"- 67x52cm: 8张")
    print(f"- 120x45cm: 3张")
    
    # 画布配置
    canvas_width_cm = 166.0
    canvas_width_px = int(canvas_width_cm * 72 / 2.54)
    max_height_px = int(500.0 * 72 / 2.54)
    
    # 生产模式测试
    arranger = UnifiedImageArranger()
    arranger.initialize(
        canvas_width_px=canvas_width_px,
        max_height_px=max_height_px,
        image_spacing_px=0,
        ppi=72,
        is_test_mode=False  # 生产模式
    )
    
    # 执行排列
    arranged_images = arranger.arrange_images(test_images)
    stats = arranger.get_layout_statistics()
    
    # 分析结果
    total_placed = len(arranged_images)
    total_rotated = sum(1 for img in arranged_images if img.get('need_rotation', False))
    
    # 按尺寸统计旋转情况
    rotation_stats = {}
    for img in arranged_images:
        size_key = f"{img.get('width_cm', 0)}x{img.get('height_cm', 0)}"
        if size_key not in rotation_stats:
            rotation_stats[size_key] = {'total': 0, 'rotated': 0}
        rotation_stats[size_key]['total'] += 1
        if img.get('need_rotation', False):
            rotation_stats[size_key]['rotated'] += 1
    
    print(f"\n📊 排列结果：")
    print(f"  - 成功排列：{total_placed}/{len(test_images)} 张")
    print(f"  - 画布利用率：{stats['utilization_percent']:.2f}%")
    print(f"  - 总旋转图片：{total_rotated}/{total_placed} 张")
    
    print(f"\n📋 按尺寸统计：")
    for size_key, stat in rotation_stats.items():
        print(f"  - {size_key}cm: {stat['rotated']}/{stat['total']} 张旋转")
    
    success = stats['utilization_percent'] >= 85.0 and total_rotated >= len(test_images) * 0.8
    
    print(f"\n✅ 验证结果：")
    if success:
        print(f"  ✅ 混合场景优化成功！")
    else:
        print(f"  ⚠️ 混合场景需要进一步优化")
    
    return {
        'scenario': '混合场景',
        'total_placed': total_placed,
        'total_images': len(test_images),
        'utilization': stats['utilization_percent'],
        'rotation_count': total_rotated,
        'rotation_stats': rotation_stats,
        'success': success
    }

if __name__ == "__main__":
    print("最终生产模式验证测试")
    print("验证用户报告中的所有场景是否都能被正确处理")
    
    # 执行所有测试
    results = []
    
    # 测试1：用户报告1场景
    result1 = test_user_report_scenario_1()
    results.append(result1)
    
    # 测试2：用户报告2场景
    result2 = test_user_report_scenario_2()
    results.append(result2)
    
    # 测试3：混合场景
    result3 = test_mixed_scenarios()
    results.append(result3)
    
    # 总结报告
    print(f"\n" + "=" * 80)
    print("最终验证总结")
    print("=" * 80)
    
    all_success = True
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['scenario']}: {status}")
        print(f"  - 排列成功率：{result['total_placed']}/{result['total_images']} ({result['total_placed']/result['total_images']*100:.1f}%)")
        print(f"  - 画布利用率：{result['utilization']:.2f}%")
        
        if 'rotation_137x51' in result:
            print(f"  - 137x51cm旋转：{result['rotation_137x51']}/17 张")
        if 'rotation_count' in result:
            print(f"  - 旋转图片：{result['rotation_count']} 张")
        
        if not result['success']:
            all_success = False
        print()
    
    print("=" * 80)
    if all_success:
        print("🎉 所有测试场景验证成功！")
        print("✅ 生产模式下的rectpack算法优化完全达到预期效果")
        print("✅ 用户报告中的横向图片旋转问题已完全解决")
        print("✅ 横向利用率显著提升，达到93%以上目标")
    else:
        print("⚠️ 部分测试场景需要进一步优化")
    print("=" * 80)
