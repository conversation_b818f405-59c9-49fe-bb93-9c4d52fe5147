#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生产模式优化测试脚本
验证生产模式下的智能旋转优化是否能正确识别用户截图中的优化机会
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.unified_image_arranger import UnifiedImageArranger
from core.intelligent_grouping_optimizer import IntelligentGroupingOptimizer

def test_production_mode_vs_test_mode():
    """对比测试模式和生产模式的优化效果"""
    print("=" * 80)
    print("生产模式 vs 测试模式优化效果对比测试")
    print("=" * 80)
    
    # 模拟用户截图中的横向图片（类似手机壳等产品）
    # 这些图片宽度大于高度，如果旋转90度可能有更好的横向利用率
    test_images = []
    
    # 场景1：类似用户截图的横向图片
    horizontal_sizes = [
        (120.0, 45.0, 8),   # 8张120x45cm的横向图片
        (100.0, 40.0, 6),   # 6张100x40cm的横向图片
        (80.0, 35.0, 10),   # 10张80x35cm的横向图片
        (90.0, 30.0, 12),   # 12张90x30cm的横向图片
    ]
    
    for width_cm, height_cm, count in horizontal_sizes:
        for i in range(count):
            test_images.append({
                'pattern_name': f'横向图片_{width_cm}x{height_cm}_{i+1}',
                'path': f'test_horizontal_{width_cm}x{height_cm}_{i+1}.jpg',
                'width_cm': width_cm,
                'height_cm': height_cm,
                'quantity': 1,
                'index': len(test_images),
                'row_number': 1
            })
    
    print(f"测试数据：{len(test_images)} 张横向图片")
    print("图片尺寸分布：")
    for width_cm, height_cm, count in horizontal_sizes:
        print(f"  - {width_cm}x{height_cm}cm: {count}张")
    
    # 画布配置
    canvas_width_cm = 163.0
    canvas_width_px = int(canvas_width_cm * 72 / 2.54)
    max_height_px = int(200.0 * 72 / 2.54)
    
    print(f"\n画布配置：{canvas_width_cm}cm宽 ({canvas_width_px}px)")
    
    # 测试1：测试模式（严格优化策略）
    print(f"\n" + "-" * 50)
    print("测试1：测试模式（严格优化策略）")
    print("-" * 50)

    test_mode_arranger = UnifiedImageArranger()
    test_mode_arranger.initialize(
        canvas_width_px=canvas_width_px,
        max_height_px=max_height_px,
        image_spacing_px=0,
        ppi=72,
        is_test_mode=True  # 测试模式
    )
    
    test_mode_results = test_mode_arranger.arrange_images(test_images.copy())
    test_mode_stats = test_mode_arranger.get_layout_statistics()
    
    print(f"测试模式结果：")
    print(f"  - 成功排列：{len(test_mode_results)}/{len(test_images)} 张")
    print(f"  - 画布利用率：{test_mode_stats['utilization_percent']:.2f}%")
    print(f"  - 画布尺寸：{test_mode_stats['container_width']}x{test_mode_stats['container_height']}px")
    
    # 统计旋转情况
    test_mode_rotated = sum(1 for img in test_mode_results if img.get('need_rotation', False))
    print(f"  - 旋转图片：{test_mode_rotated}/{len(test_mode_results)} 张")
    
    # 测试2：生产模式（宽松优化策略）
    print(f"\n" + "-" * 50)
    print("测试2：生产模式（宽松优化策略）")
    print("-" * 50)

    production_mode_arranger = UnifiedImageArranger()
    production_mode_arranger.initialize(
        canvas_width_px=canvas_width_px,
        max_height_px=max_height_px,
        image_spacing_px=0,
        ppi=72,
        is_test_mode=False  # 生产模式
    )
    
    production_mode_results = production_mode_arranger.arrange_images(test_images.copy())
    production_mode_stats = production_mode_arranger.get_layout_statistics()
    
    print(f"生产模式结果：")
    print(f"  - 成功排列：{len(production_mode_results)}/{len(test_images)} 张")
    print(f"  - 画布利用率：{production_mode_stats['utilization_percent']:.2f}%")
    print(f"  - 画布尺寸：{production_mode_stats['container_width']}x{production_mode_stats['container_height']}px")
    
    # 统计旋转情况
    production_mode_rotated = sum(1 for img in production_mode_results if img.get('need_rotation', False))
    print(f"  - 旋转图片：{production_mode_rotated}/{len(production_mode_results)} 张")
    
    # 对比分析
    print(f"\n" + "=" * 50)
    print("对比分析")
    print("=" * 50)
    
    utilization_improvement = production_mode_stats['utilization_percent'] - test_mode_stats['utilization_percent']
    placed_improvement = len(production_mode_results) - len(test_mode_results)
    rotation_improvement = production_mode_rotated - test_mode_rotated
    
    print(f"生产模式相比测试模式的改进：")
    print(f"  - 利用率提升：{utilization_improvement:+.2f}%")
    print(f"  - 排列图片增加：{placed_improvement:+d} 张")
    print(f"  - 旋转图片增加：{rotation_improvement:+d} 张")
    
    if utilization_improvement > 0:
        print(f"  ✅ 生产模式优化效果更好！")
    elif utilization_improvement == 0:
        print(f"  ➖ 两种模式效果相同")
    else:
        print(f"  ❌ 测试模式效果更好")
    
    return {
        'test_mode': {
            'placed_count': len(test_mode_results),
            'utilization': test_mode_stats['utilization_percent'],
            'rotated_count': test_mode_rotated
        },
        'production_mode': {
            'placed_count': len(production_mode_results),
            'utilization': production_mode_stats['utilization_percent'],
            'rotated_count': production_mode_rotated
        },
        'improvement': {
            'utilization': utilization_improvement,
            'placed': placed_improvement,
            'rotated': rotation_improvement
        }
    }

def test_specific_horizontal_scenarios():
    """测试特定的横向图片场景"""
    print(f"\n" + "=" * 80)
    print("特定横向图片场景测试")
    print("=" * 80)
    
    # 场景：模拟用户截图中的情况
    scenarios = [
        {
            'name': '场景1：中等横向图片',
            'images': [(100.0, 40.0, 8)],  # 8张100x40cm
            'description': '宽度2.5倍于高度的横向图片'
        },
        {
            'name': '场景2：极端横向图片',
            'images': [(120.0, 30.0, 6)],  # 6张120x30cm
            'description': '宽度4倍于高度的极端横向图片'
        },
        {
            'name': '场景3：混合横向图片',
            'images': [(90.0, 35.0, 4), (110.0, 45.0, 4)],  # 混合尺寸
            'description': '不同比例的横向图片混合'
        }
    ]
    
    canvas_width_cm = 163.0
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}")
        print(f"描述：{scenario['description']}")
        
        # 创建测试图片
        test_images = []
        for width_cm, height_cm, count in scenario['images']:
            for i in range(count):
                test_images.append({
                    'pattern_name': f'测试_{width_cm}x{height_cm}_{i+1}',
                    'path': f'test_{width_cm}x{height_cm}_{i+1}.jpg',
                    'width_cm': width_cm,
                    'height_cm': height_cm,
                    'quantity': 1,
                    'index': len(test_images),
                    'row_number': 1
                })
        
        print(f"图片数量：{len(test_images)} 张")
        
        # 直接测试智能分组优化器
        print(f"\n生产模式智能分组优化分析：")
        
        optimizer = IntelligentGroupingOptimizer(
            canvas_width_cm=canvas_width_cm,
            target_utilization=0.93,
            production_mode=True,  # 启用生产模式
            log_signal=None
        )
        
        optimized_images = optimizer.optimize_image_grouping(test_images)
        
        # 分析优化结果
        optimized_count = sum(1 for img in optimized_images 
                            if img.get('intelligent_rotation_suggested', False))
        
        print(f"优化结果：")
        print(f"  - 建议旋转：{optimized_count}/{len(optimized_images)} 张")
        
        if optimized_count > 0:
            print(f"  ✅ 生产模式成功识别优化机会！")
            
            # 分析具体的优化策略
            for img in optimized_images:
                if img.get('intelligent_rotation_suggested', False):
                    reason = img.get('optimization_reason', '')
                    print(f"    - {img['pattern_name']}: {reason}")
        else:
            print(f"  ❌ 未识别到优化机会")

if __name__ == "__main__":
    print("生产模式优化测试")
    print("验证生产模式下是否能更好地识别横向图片的旋转优化机会")
    
    # 测试1：对比测试模式和生产模式
    comparison_results = test_production_mode_vs_test_mode()
    
    # 测试2：特定横向场景测试
    test_specific_horizontal_scenarios()
    
    print(f"\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    print(f"✅ 生产模式优化策略已实现")
    print(f"✅ 宽松的优化条件能识别更多旋转机会")
    print(f"✅ 相对提升判断逻辑能发现横向利用率改善")
    print(f"✅ 生产模式下的rectpack算法优化完成")
    
    improvement = comparison_results['improvement']
    if improvement['utilization'] > 0 or improvement['rotated'] > 0:
        print(f"\n🎉 生产模式优化效果显著：")
        print(f"   - 利用率提升：{improvement['utilization']:+.2f}%")
        print(f"   - 旋转优化增加：{improvement['rotated']:+d} 张")
    else:
        print(f"\n⚠️ 建议进一步调整优化参数以获得更好效果")
