#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack约束管理器
处理宽度间隙限制、边长微调等约束条件和优化策略

核心约束条件：
1. 宽度间隙限制：画布宽度 - 已放置图片总宽度 ≤ 10cm
2. 边长微调：每张图片边长可缩减最多1cm

优化策略：
1. 智能旋转决策
2. 边长微调优化
3. 布局效率优化
"""

import logging
from typing import List, Dict, Any, Tuple, Optional
import copy

# 配置日志
log = logging.getLogger(__name__)

class ConstraintViolation:
    """约束违反信息"""
    
    def __init__(self, constraint_type: str, current_value: float, 
                 limit_value: float, message: str):
        self.constraint_type = constraint_type
        self.current_value = current_value
        self.limit_value = limit_value
        self.message = message
        
    def __str__(self):
        return f"{self.constraint_type}: {self.message} (当前: {self.current_value:.1f}, 限制: {self.limit_value:.1f})"

class ImageAdjustment:
    """图片调整信息"""
    
    def __init__(self, image_id: str, original_width: float, original_height: float,
                 adjusted_width: float, adjusted_height: float, rotated: bool = False,
                 width_reduction: float = 0.0, height_reduction: float = 0.0):
        self.image_id = image_id
        self.original_width = original_width
        self.original_height = original_height
        self.adjusted_width = adjusted_width
        self.adjusted_height = adjusted_height
        self.rotated = rotated
        self.width_reduction = width_reduction
        self.height_reduction = height_reduction
        
    def get_total_reduction(self) -> float:
        """获取总缩减量"""
        return self.width_reduction + self.height_reduction
        
    def __str__(self):
        rotation_str = " (旋转90°)" if self.rotated else ""
        reduction_str = f" [缩减: 宽{self.width_reduction:.1f}cm, 高{self.height_reduction:.1f}cm]" if self.get_total_reduction() > 0 else ""
        return f"{self.image_id}: {self.original_width}x{self.original_height}cm → {self.adjusted_width}x{self.adjusted_height}cm{rotation_str}{reduction_str}"

class RectPackConstraintManager:
    """RectPack约束管理器"""
    
    def __init__(self, canvas_width_cm: float, max_width_gap_cm: float = 10.0, 
                 max_edge_reduction_cm: float = 1.0, log_signal=None):
        """
        初始化约束管理器
        
        Args:
            canvas_width_cm: 画布宽度（厘米）
            max_width_gap_cm: 最大宽度间隙（厘米）
            max_edge_reduction_cm: 最大边长缩减（厘米）
            log_signal: 日志信号
        """
        self.canvas_width_cm = canvas_width_cm
        self.max_width_gap_cm = max_width_gap_cm
        self.max_edge_reduction_cm = max_edge_reduction_cm
        self.log_signal = log_signal
        
        # 约束检查统计
        self.constraint_checks = 0
        self.constraint_violations = 0
        self.adjustments_applied = 0
        
        # 调整记录
        self.image_adjustments = []
        
    def emit_log(self, message: str):
        """发送日志信息"""
        if self.log_signal:
            self.log_signal.emit(message)
        else:
            log.info(message)
    
    def check_width_gap_constraint(self, placed_images: List[Dict[str, Any]]) -> Optional[ConstraintViolation]:
        """
        检查宽度间隙约束
        
        Args:
            placed_images: 已放置的图片列表
            
        Returns:
            ConstraintViolation: 如果违反约束则返回违反信息，否则返回None
        """
        self.constraint_checks += 1
        
        if not placed_images:
            return None
            
        # 计算已放置图片的总宽度（转换为厘米）
        total_width_cm = 0.0
        for img in placed_images:
            # 从像素转换为厘米（假设72 PPI）
            width_px = img.get('width', 0)
            width_cm = width_px * 2.54 / 72  # 72 PPI转换
            total_width_cm += width_cm
            
        # 计算宽度间隙
        width_gap_cm = self.canvas_width_cm - total_width_cm

        # 检查是否违反约束
        # 注意：当width_gap_cm为负数时，表示图片总宽度超出画布，这也是违反约束的
        if width_gap_cm < 0:
            # 图片总宽度超出画布
            self.constraint_violations += 1
            violation = ConstraintViolation(
                constraint_type="宽度超出限制",
                current_value=abs(width_gap_cm),
                limit_value=0.0,
                message=f"图片总宽度超出画布 {abs(width_gap_cm):.1f}cm"
            )
            return violation
        elif width_gap_cm > self.max_width_gap_cm:
            # 宽度间隙过大
            self.constraint_violations += 1
            violation = ConstraintViolation(
                constraint_type="宽度间隙限制",
                current_value=width_gap_cm,
                limit_value=self.max_width_gap_cm,
                message=f"宽度间隙 {width_gap_cm:.1f}cm 超过限制 {self.max_width_gap_cm:.1f}cm"
            )
            return violation
            
        return None
    
    def calculate_optimal_adjustments(self, images: List[Dict[str, Any]], 
                                    target_total_width_cm: float) -> List[ImageAdjustment]:
        """
        计算最优的图片调整方案
        
        Args:
            images: 图片列表
            target_total_width_cm: 目标总宽度（厘米）
            
        Returns:
            List[ImageAdjustment]: 调整方案列表
        """
        adjustments = []
        
        # 计算当前总宽度
        current_total_width_cm = 0.0
        for img in images:
            width_cm = img.get('width_cm', 0)
            current_total_width_cm += width_cm
            
        # 计算需要缩减的总量
        total_reduction_needed = current_total_width_cm - target_total_width_cm
        
        if total_reduction_needed <= 0:
            return adjustments  # 不需要调整
            
        self.emit_log(f"需要缩减总宽度: {total_reduction_needed:.1f}cm")
        
        # 为每张图片分配缩减量
        num_images = len(images)
        if num_images == 0:
            return adjustments
            
        # 平均分配缩减量，但不超过每张图片的最大缩减限制
        avg_reduction_per_image = total_reduction_needed / num_images
        max_reduction_per_image = min(avg_reduction_per_image, self.max_edge_reduction_cm)
        
        remaining_reduction = total_reduction_needed
        
        for img in images:
            image_id = img.get('unique_id', img.get('name', 'Unknown'))
            original_width = img.get('width_cm', 0)
            original_height = img.get('height_cm', 0)
            
            # 计算这张图片的缩减量
            if remaining_reduction > 0:
                # 优先缩减宽度，因为我们主要关心宽度间隙
                width_reduction = min(max_reduction_per_image, remaining_reduction, self.max_edge_reduction_cm)
                height_reduction = 0.0
                
                # 如果宽度缩减不够，再考虑高度缩减
                if width_reduction < max_reduction_per_image and remaining_reduction > width_reduction:
                    additional_reduction_needed = min(
                        max_reduction_per_image - width_reduction,
                        remaining_reduction - width_reduction,
                        self.max_edge_reduction_cm
                    )
                    height_reduction = additional_reduction_needed
                    
                adjusted_width = original_width - width_reduction
                adjusted_height = original_height - height_reduction
                
                adjustment = ImageAdjustment(
                    image_id=image_id,
                    original_width=original_width,
                    original_height=original_height,
                    adjusted_width=adjusted_width,
                    adjusted_height=adjusted_height,
                    width_reduction=width_reduction,
                    height_reduction=height_reduction
                )
                
                adjustments.append(adjustment)
                remaining_reduction -= (width_reduction + height_reduction)
            else:
                # 不需要调整的图片
                adjustment = ImageAdjustment(
                    image_id=image_id,
                    original_width=original_width,
                    original_height=original_height,
                    adjusted_width=original_width,
                    adjusted_height=original_height
                )
                adjustments.append(adjustment)
                
        return adjustments
    
    def apply_edge_reduction_strategy(self, width_cm: float, height_cm: float, 
                                    image_data: Dict[str, Any]) -> Tuple[float, float, bool]:
        """
        应用边长缩减策略
        
        Args:
            width_cm: 原始宽度（厘米）
            height_cm: 原始高度（厘米）
            image_data: 图片数据
            
        Returns:
            Tuple[float, float, bool]: (调整后宽度, 调整后高度, 是否进行了调整)
        """
        original_width = width_cm
        original_height = height_cm
        
        # 检查是否已经有调整建议
        image_id = image_data.get('unique_id', image_data.get('name', 'Unknown'))
        
        # 查找是否有预计算的调整方案
        for adjustment in self.image_adjustments:
            if adjustment.image_id == image_id:
                self.emit_log(f"应用预计算的边长调整: {adjustment}")
                return adjustment.adjusted_width, adjustment.adjusted_height, True
                
        # 如果没有预计算的调整，应用默认的微调策略
        # 优先缩减较长的边
        if width_cm > height_cm:
            # 宽度较长，优先缩减宽度
            reduction = min(self.max_edge_reduction_cm, width_cm * 0.02)  # 最多缩减2%或1cm
            adjusted_width = width_cm - reduction
            adjusted_height = height_cm
        else:
            # 高度较长，优先缩减高度
            reduction = min(self.max_edge_reduction_cm, height_cm * 0.02)  # 最多缩减2%或1cm
            adjusted_width = width_cm
            adjusted_height = height_cm - reduction
            
        # 记录调整
        if adjusted_width != original_width or adjusted_height != original_height:
            adjustment = ImageAdjustment(
                image_id=image_id,
                original_width=original_width,
                original_height=original_height,
                adjusted_width=adjusted_width,
                adjusted_height=adjusted_height,
                width_reduction=original_width - adjusted_width,
                height_reduction=original_height - adjusted_height
            )
            self.image_adjustments.append(adjustment)
            self.adjustments_applied += 1
            
            self.emit_log(f"应用边长微调: {adjustment}")
            return adjusted_width, adjusted_height, True
            
        return width_cm, height_cm, False
    
    def validate_layout_constraints(self, placed_images: List[Dict[str, Any]]) -> List[ConstraintViolation]:
        """
        验证布局是否满足所有约束条件
        
        Args:
            placed_images: 已放置的图片列表
            
        Returns:
            List[ConstraintViolation]: 违反的约束列表
        """
        violations = []
        
        # 检查宽度间隙约束
        width_gap_violation = self.check_width_gap_constraint(placed_images)
        if width_gap_violation:
            violations.append(width_gap_violation)
            
        # 可以在这里添加其他约束检查
        
        return violations
    
    def get_constraint_statistics(self) -> Dict[str, Any]:
        """
        获取约束检查统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'constraint_checks': self.constraint_checks,
            'constraint_violations': self.constraint_violations,
            'adjustments_applied': self.adjustments_applied,
            'violation_rate': self.constraint_violations / max(self.constraint_checks, 1),
            'adjustment_rate': self.adjustments_applied / max(len(self.image_adjustments), 1),
            'total_adjustments': len(self.image_adjustments)
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.constraint_checks = 0
        self.constraint_violations = 0
        self.adjustments_applied = 0
        self.image_adjustments.clear()
