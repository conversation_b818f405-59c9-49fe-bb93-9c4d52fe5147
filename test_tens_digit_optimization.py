#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
十位数优化算法测试脚本

测试新的基于十位数的智能优化策略
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.intelligent_grouping_optimizer import IntelligentGroupingOptimizer

def test_tens_digit_optimization():
    """测试十位数优化算法"""
    
    print("=" * 80)
    print("十位数优化算法测试")
    print("=" * 80)
    
    # 测试场景1：163cm画布（用户报告的实际场景）
    canvas_width_cm = 163.0
    target_tens_digit = int((canvas_width_cm - 10) / 10)  # (163-10)/10 = 15.3 → 15
    
    print(f"测试场景：画布宽度 {canvas_width_cm}cm")
    print(f"目标十位数：{target_tens_digit}")
    print()
    
    # 创建优化器
    optimizer = IntelligentGroupingOptimizer(
        canvas_width_cm=canvas_width_cm,
        target_utilization=0.93,
        production_mode=True,
        log_signal=None
    )
    
    # 测试图片数据（基于用户报告）
    test_images = [
        # 137x51cm图片（17张）- 应该被优化
        *[{'pattern_name': f'137x51_{i}', 'width_cm': 137.0, 'height_cm': 51.0} for i in range(17)],
        
        # 67x52cm图片（10张）- 应该被优化
        *[{'pattern_name': f'67x52_{i}', 'width_cm': 67.0, 'height_cm': 52.0} for i in range(10)],
        
        # 90x60cm图片（8张）- 应该被优化
        *[{'pattern_name': f'90x60_{i}', 'width_cm': 90.0, 'height_cm': 60.0} for i in range(8)],
        
        # 80x70cm图片（6张）- 应该被优化
        *[{'pattern_name': f'80x70_{i}', 'width_cm': 80.0, 'height_cm': 70.0} for i in range(6)],
        
        # 30x40cm图片（15张）- 应该被优化
        *[{'pattern_name': f'30x40_{i}', 'width_cm': 30.0, 'height_cm': 40.0} for i in range(15)],
        
        # 其他尺寸图片（不应该被优化）
        *[{'pattern_name': f'100x25_{i}', 'width_cm': 100.0, 'height_cm': 25.0} for i in range(5)],
        *[{'pattern_name': f'120x35_{i}', 'width_cm': 120.0, 'height_cm': 35.0} for i in range(5)],
    ]
    
    print(f"测试图片总数：{len(test_images)}张")
    print()
    
    # 手动分析十位数
    print("手动十位数分析：")
    print(f"137x51cm → 51的十位数=5, {target_tens_digit}÷5={target_tens_digit//5}张, 利用率={(target_tens_digit//5)*51/canvas_width_cm:.1%}")
    print(f"67x52cm → 52的十位数=5, {target_tens_digit}÷5={target_tens_digit//5}张, 利用率={(target_tens_digit//5)*52/canvas_width_cm:.1%}")
    print(f"90x60cm → 60的十位数=6, {target_tens_digit}÷6={target_tens_digit//6}张, 利用率={(target_tens_digit//6)*60/canvas_width_cm:.1%}")
    print(f"80x70cm → 70的十位数=7, {target_tens_digit}÷7={target_tens_digit//7}张, 利用率={(target_tens_digit//7)*70/canvas_width_cm:.1%}")
    print(f"30x40cm → 40的十位数=4, {target_tens_digit}÷4={target_tens_digit//4}张, 利用率={(target_tens_digit//4)*40/canvas_width_cm:.1%}")
    print()
    
    # 执行优化
    print("执行智能分组优化...")
    print("-" * 60)
    
    optimized_images = optimizer.optimize_image_grouping(test_images)
    
    print("-" * 60)
    print()
    
    # 统计结果
    total_rotation_suggested = sum(1 for img in optimized_images 
                                 if img.get('intelligent_rotation_suggested', False))
    
    print("优化结果统计：")
    print(f"总图片数：{len(optimized_images)}")
    print(f"建议旋转：{total_rotation_suggested}张")
    print(f"旋转比例：{total_rotation_suggested/len(optimized_images):.1%}")
    print()
    
    # 按尺寸分组统计
    size_rotation_stats = {}
    for img in optimized_images:
        width_cm = img.get('width_cm', 0)
        height_cm = img.get('height_cm', 0)
        size_key = f"{width_cm}x{height_cm}"
        
        if size_key not in size_rotation_stats:
            size_rotation_stats[size_key] = {'total': 0, 'rotated': 0}
        
        size_rotation_stats[size_key]['total'] += 1
        if img.get('intelligent_rotation_suggested', False):
            size_rotation_stats[size_key]['rotated'] += 1
    
    print("各尺寸旋转统计：")
    for size_key, stats in size_rotation_stats.items():
        rotation_rate = stats['rotated'] / stats['total'] if stats['total'] > 0 else 0
        print(f"  {size_key}cm: {stats['rotated']}/{stats['total']} ({rotation_rate:.1%})")
    
    print()
    
    # 验证预期结果
    print("验证预期结果：")
    
    expected_optimizations = [
        ('137.0x51.0', True, "十位数5，3张/行，93.9%利用率"),
        ('67.0x52.0', True, "十位数5，3张/行，95.7%利用率"),
        ('90.0x60.0', True, "十位数6，2张/行，73.6%利用率"),
        ('80.0x70.0', True, "十位数7，2张/行，85.9%利用率"),
        ('30.0x40.0', True, "十位数4，3张/行，73.6%利用率"),
        ('100.0x25.0', False, "十位数2，利用率不足"),
        ('120.0x35.0', False, "十位数3，利用率不足"),
    ]
    
    for size_key, expected_rotation, reason in expected_optimizations:
        actual_rotation_rate = size_rotation_stats.get(size_key, {}).get('rotated', 0) / size_rotation_stats.get(size_key, {}).get('total', 1)
        actual_rotation = actual_rotation_rate > 0.5
        
        status = "✅" if actual_rotation == expected_rotation else "❌"
        print(f"  {status} {size_key}: 预期{'旋转' if expected_rotation else '不旋转'}, 实际{'旋转' if actual_rotation else '不旋转'} - {reason}")

if __name__ == "__main__":
    test_tens_digit_optimization()
