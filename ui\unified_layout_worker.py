#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统一布局工作器模块
使用新的RectPack统一排列算法的工作线程

特性：
1. 使用RectPack算法替换复杂的分类逻辑
2. 提供更高的画布利用率
3. 简化的处理流程
4. 保持与现有UI的兼容性
"""

import os
import sys
import time
import logging
import traceback
from typing import List, Dict, Any, Optional
from PyQt6.QtCore import QThread, pyqtSignal, QMutex, QMutexLocker

# 导入核心模块
from core.excel_processor import ExcelProcessor
from core.image_indexer_duckdb import ImageIndexerDuckDB
from core.unified_image_arranger import UnifiedImageArranger
from core.simplified_image_classifier import SimplifiedImageClassifier
from utils.photoshop_helper import PhotoshopHelper
from utils.log_file_creator import create_log_file_async
from utils.memory_manager import MemoryManager
from utils.stream_processor import StreamProcessor
import photoshop.api as ps

# 导入图片处理器
from utils.image_processor import get_image_processor

# 配置日志
from utils.log_config import get_logger
log = get_logger("UnifiedLayoutWorker")

class UnifiedLayoutWorker(QThread):
    """
    统一布局工作器类
    使用RectPack算法的工作线程
    """

    # 信号定义
    progress_signal = pyqtSignal(int)  # 进度信号 (0-100)
    log_signal = pyqtSignal(str)       # 日志信号
    error_signal = pyqtSignal(str)     # 错误信号
    finished_signal = pyqtSignal()     # 完成信号
    stage_signal = pyqtSignal(str, int, str)  # 阶段信号 (阶段名, 进度, 状态)

    def __init__(self, config_manager, image_indexer: ImageIndexerDuckDB,
                 excel_processor: ExcelProcessor, parent=None):
        """
        初始化统一布局工作器

        Args:
            config_manager: 配置管理器
            image_indexer: 图片索引器
            excel_processor: Excel处理器
            parent: 父对象
        """
        super().__init__(parent)

        self.config_manager = config_manager
        self.image_indexer = image_indexer
        self.excel_processor = excel_processor

        # 工作参数
        self.library_path = ""
        self.material_folder_path = ""
        self.canvas_width_m = 2.0
        self.max_height_cm = 5000
        self.ppi = 72
        self.image_spacing_cm = 0.1
        self.horizontal_expansion_cm = 0

        # 控制标志
        self._stop_requested = False
        self._mutex = QMutex()

        # 核心组件
        self.unified_arranger = None
        self.simplified_classifier = None

        # 性能监控
        self.memory_manager = MemoryManager()
        self.stream_processor = StreamProcessor()

        # 统计信息
        self.total_images_processed = 0
        self.successful_arrangements = 0
        self.failed_arrangements = 0
        self.start_time = None

    def set_parameters(self, library_path: str, material_folder_path: str,
                      canvas_width_m: float, max_height_cm: float, ppi: float,
                      image_spacing_cm: float, horizontal_expansion_cm: float = 0):
        """
        设置工作参数

        Args:
            library_path: 图库路径
            material_folder_path: 材质文件夹路径
            canvas_width_m: 画布宽度（米）
            max_height_cm: 最大高度（厘米）
            ppi: 每英寸像素数
            image_spacing_cm: 图片间距（厘米）
            horizontal_expansion_cm: 水平扩展（厘米）
        """
        with QMutexLocker(self._mutex):
            self.library_path = library_path
            self.material_folder_path = material_folder_path
            self.canvas_width_m = canvas_width_m
            self.max_height_cm = max_height_cm
            self.ppi = ppi
            self.image_spacing_cm = image_spacing_cm
            self.horizontal_expansion_cm = horizontal_expansion_cm

    def request_stop(self):
        """请求停止工作"""
        with QMutexLocker(self._mutex):
            self._stop_requested = True
        self.log_signal.emit("收到停止请求...")

    def is_stop_requested(self) -> bool:
        """检查是否请求停止"""
        with QMutexLocker(self._mutex):
            return self._stop_requested

    def emit_stage(self, stage_name: str, progress: int, status: str):
        """发送阶段信号"""
        self.stage_signal.emit(stage_name, progress, status)
        self.log_signal.emit(f"[{stage_name}] {status}")

    def run(self):
        """主工作流程"""
        try:
            self.start_time = time.time()
            self._stop_requested = False

            self.log_signal.emit("=" * 60)
            self.log_signal.emit("开始统一布局处理（使用图像智能排列算法）")
            self.log_signal.emit("=" * 60)

            # 阶段1: 准备工作
            self.emit_stage("准备", 0, "初始化组件...")
            if not self._initialize_components():
                return

            if self.is_stop_requested():
                return

            # 阶段2: 处理Excel文件
            self.emit_stage("检索", 10, "处理材质表格...")
            pattern_items = self._process_excel_files()
            if not pattern_items:
                self.error_signal.emit("没有找到有效的图片数据")
                return

            if self.is_stop_requested():
                return

            # 阶段3: 检索图片
            self.emit_stage("检索", 30, "检索图片文件...")
            pattern_items = self._retrieve_images(pattern_items)
            if not pattern_items:
                self.error_signal.emit("没有找到匹配的图片文件")
                return

            if self.is_stop_requested():
                return

            # 阶段4: 图片分类（简化版）
            self.emit_stage("排列", 50, "预处理图片数据...")
            processed_patterns = self._classify_images(pattern_items)
            if not processed_patterns:
                self.error_signal.emit("图片预处理失败")
                return

            if self.is_stop_requested():
                return

            # 阶段5: 统一排列
            self.emit_stage("排列", 60, "使用RectPack算法排列图片...")
            arranged_images = self._arrange_images(processed_patterns)
            if not arranged_images:
                self.error_signal.emit("图片排列失败")
                return

            if self.is_stop_requested():
                return

            # 阶段6: 输出到Photoshop
            self.emit_stage("输出", 80, "创建Photoshop画布...")
            success = self._create_photoshop_canvas(arranged_images)
            if not success:
                self.error_signal.emit("Photoshop画布创建失败")
                return

            # 完成
            self.emit_stage("输出", 100, "处理完成")
            self._log_final_statistics()

        except Exception as e:
            error_msg = f"统一布局处理发生错误: {str(e)}"
            self.error_signal.emit(error_msg)
            log.error(error_msg, exc_info=True)
        finally:
            self.finished_signal.emit()

    def _initialize_components(self) -> bool:
        """初始化组件"""
        try:
            # 初始化简化分类器
            self.simplified_classifier = SimplifiedImageClassifier(log_signal=self.log_signal)

            # 检查是否为测试模式
            test_mode_settings = self.config_manager.get_test_mode_settings()
            is_test_mode = test_mode_settings.get('is_test_mode', False)

            # 计算画布参数
            canvas_width_cm = self.canvas_width_m * 100 + self.horizontal_expansion_cm
            canvas_width_px = int(canvas_width_cm * 0.393701 * self.ppi)
            max_height_px = int(self.max_height_cm * 0.393701 * self.ppi)
            image_spacing_px = int(self.image_spacing_cm * 0.393701 * self.ppi)

            # 初始化统一排列器，传递测试模式参数
            self.unified_arranger = UnifiedImageArranger(log_signal=self.log_signal)
            self.unified_arranger.initialize(
                canvas_width_px=canvas_width_px,
                max_height_px=max_height_px,
                image_spacing_px=image_spacing_px,
                ppi=self.ppi,
                is_test_mode=is_test_mode  # 传递测试模式标志
            )

            self.log_signal.emit(f"画布参数: 宽度={canvas_width_cm:.1f}cm ({canvas_width_px}px), 最大高度={self.max_height_cm}cm ({max_height_px}px)")
            self.log_signal.emit(f"图片间距: {self.image_spacing_cm}cm ({image_spacing_px}px), PPI={self.ppi}")

            return True

        except Exception as e:
            self.error_signal.emit(f"组件初始化失败: {str(e)}")
            return False

    def _process_excel_files(self) -> List[Dict[str, Any]]:
        """处理Excel文件"""
        try:
            if not os.path.exists(self.material_folder_path):
                self.error_signal.emit(f"材质文件夹不存在: {self.material_folder_path}")
                return []

            excel_files = [f for f in os.listdir(self.material_folder_path)
                          if f.lower().endswith(('.xlsx', '.xls'))]

            if not excel_files:
                self.error_signal.emit("材质文件夹中没有找到Excel文件")
                return []

            all_patterns = []
            for excel_file in excel_files:
                if self.is_stop_requested():
                    break

                excel_path = os.path.join(self.material_folder_path, excel_file)
                self.log_signal.emit(f"处理Excel文件: {excel_file}")

                patterns = self.excel_processor.process_excel_file(excel_path)
                if patterns:
                    all_patterns.extend(patterns)
                    self.log_signal.emit(f"从 {excel_file} 提取了 {len(patterns)} 个图案")

            self.log_signal.emit(f"总共提取了 {len(all_patterns)} 个图案")
            return all_patterns

        except Exception as e:
            self.error_signal.emit(f"处理Excel文件时发生错误: {str(e)}")
            return []

    def _retrieve_images(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检索图片文件"""
        try:
            retrieved_patterns = []
            total_patterns = len(pattern_items)
            found_count = 0
            not_found_count = 0
            error_count = 0

            self.log_signal.emit(f"开始检索 {total_patterns} 个图案的图片文件...")

            # 批量处理，每100个为一批
            batch_size = 100
            for batch_start in range(0, total_patterns, batch_size):
                if self.is_stop_requested():
                    break

                batch_end = min(batch_start + batch_size, total_patterns)
                batch_patterns = pattern_items[batch_start:batch_end]

                self.log_signal.emit(f"处理第 {batch_start//batch_size + 1} 批次（{batch_start+1}-{batch_end}/{total_patterns}）")

                for i, pattern in enumerate(batch_patterns):
                    actual_index = batch_start + i

                    if self.is_stop_requested():
                        break

                    pattern_name = pattern.get('pattern_name', '')
                    if not pattern_name:
                        continue

                    # 检查是否已经有图片路径且文件存在
                    existing_path = pattern.get('path', '')
                    if existing_path and existing_path != '未入库' and os.path.exists(existing_path):
                        retrieved_patterns.append(pattern)
                        found_count += 1
                    else:
                        # 使用图片索引器查找图片，添加异常处理
                        try:
                            image_path = self.image_indexer.find_image(pattern_name)
                            if image_path and os.path.exists(image_path):
                                pattern['path'] = image_path
                                retrieved_patterns.append(pattern)
                                found_count += 1
                            else:
                                self.log_signal.emit(f"未找到图片: {pattern_name}")
                                not_found_count += 1
                        except Exception as e:
                            self.log_signal.emit(f"查找图片时发生错误 {pattern_name}: {str(e)}")
                            log.error(f"查找图片时发生错误 {pattern_name}: {str(e)}", exc_info=True)
                            error_count += 1
                            not_found_count += 1

                    # 更新进度
                    progress = int((actual_index + 1) / total_patterns * 20) + 30  # 30-50%
                    self.progress_signal.emit(progress)

                # 每批次处理后输出进度
                self.log_signal.emit(f"第 {batch_start//batch_size + 1} 批次完成，已找到 {found_count} 个图片")

            # 最终统计
            if error_count > 0:
                self.log_signal.emit(f"图片检索结果: 成功 {found_count} 个，未找到 {not_found_count} 个，错误 {error_count} 个")
            else:
                self.log_signal.emit(f"图片检索结果: 成功 {found_count} 个，未找到 {not_found_count} 个")

            self.log_signal.emit(f"最终可用于排列的图片: {len(retrieved_patterns)} 个")

            if len(retrieved_patterns) == 0:
                self.log_signal.emit("警告: 没有找到任何可用的图片文件")
                self.log_signal.emit("请检查:")
                self.log_signal.emit("1. 图片索引是否已正确初始化")
                self.log_signal.emit("2. 图片文件是否存在于指定目录")
                self.log_signal.emit("3. 图案名称是否与图片文件名匹配")
                if error_count > 0:
                    self.log_signal.emit("4. 检查上述错误信息以获取更多细节")

            return retrieved_patterns

        except Exception as e:
            self.error_signal.emit(f"检索图片时发生错误: {str(e)}")
            return []

    def _classify_images(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分类图片（简化版）"""
        try:
            # 使用简化分类器预处理图片
            processed_patterns = self.simplified_classifier.optimize_for_rectpack(pattern_items)

            self.log_signal.emit(f"图片预处理完成: {len(processed_patterns)} 个图片")
            return processed_patterns

        except Exception as e:
            self.error_signal.emit(f"图片分类时发生错误: {str(e)}")
            return []

    def _arrange_images(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """排列图片"""
        try:
            # 使用统一排列器排列所有图片
            arranged_images = self.unified_arranger.arrange_images(pattern_items)

            # 尝试优化布局
            if arranged_images:
                self.log_signal.emit("尝试优化布局以提高画布利用率...")
                self.unified_arranger.optimize_layout()

            # 获取布局统计信息
            stats = self.unified_arranger.get_layout_statistics()
            if stats:
                self.log_signal.emit(f"布局统计: 利用率={stats.get('utilization_percent', 0):.2f}%, "
                                   f"画布尺寸={stats.get('container_width', 0)}x{stats.get('container_height', 0)}px")

            self.successful_arrangements = len(arranged_images)
            self.failed_arrangements = len(pattern_items) - len(arranged_images)

            return arranged_images

        except Exception as e:
            self.error_signal.emit(f"图片排列时发生错误: {str(e)}")
            return []

    def _create_photoshop_canvas(self, arranged_images: List[Dict[str, Any]]) -> bool:
        """创建Photoshop画布"""
        try:
            # 检查是否处于测试模式
            test_mode_settings = self.config_manager.get_test_mode_settings()
            is_test_mode = test_mode_settings.get('is_test_mode', False)

            if is_test_mode:
                self.log_signal.emit("测试模式: 跳过图像编辑软件画布创建")
                return True

            # 检查Photoshop连接
            success, message = PhotoshopHelper.check_photoshop()
            if not success:
                self.error_signal.emit(f"Photoshop未运行: {message}")
                return False

            # 获取画布尺寸
            stats = self.unified_arranger.get_layout_statistics()
            canvas_width_px = stats.get('container_width', 0)
            canvas_height_px = stats.get('container_height', 0)

            if canvas_width_px <= 0 or canvas_height_px <= 0:
                self.error_signal.emit("无效的画布尺寸")
                return False

            # 创建Photoshop文档
            self.log_signal.emit(f"创建图像编辑软件画布: {canvas_width_px}x{canvas_height_px}px")

            # 使用PhotoshopHelper创建画布和放置图片
            success = PhotoshopHelper.create_canvas_and_place_images(
                arranged_images,
                canvas_width_px,
                canvas_height_px,
                self.ppi,
                log_signal=self.log_signal,
                progress_signal=self.progress_signal
            )

            if success:
                self.log_signal.emit("图像编辑软件画布创建成功")
            else:
                self.error_signal.emit("Photoshop画布创建失败")

            return success

        except Exception as e:
            self.error_signal.emit(f"创建Photoshop画布时发生错误: {str(e)}")
            return False

    def _log_final_statistics(self):
        """记录最终统计信息"""
        try:
            elapsed_time = time.time() - self.start_time if self.start_time else 0

            self.log_signal.emit("=" * 60)
            self.log_signal.emit("处理完成统计")
            self.log_signal.emit("=" * 60)
            self.log_signal.emit(f"总处理时间: {elapsed_time:.2f}秒")
            self.log_signal.emit(f"成功排列: {self.successful_arrangements} 个图片")
            self.log_signal.emit(f"排列失败: {self.failed_arrangements} 个图片")

            if elapsed_time > 0:
                speed = self.successful_arrangements / elapsed_time
                self.log_signal.emit(f"平均速度: {speed:.2f} 图片/秒")

            # 获取最终布局统计
            stats = self.unified_arranger.get_layout_statistics()
            if stats:
                self.log_signal.emit(f"最终画布利用率: {stats.get('utilization_percent', 0):.2f}%")
                self.log_signal.emit(f"最终画布尺寸: {stats.get('container_width', 0)}x{stats.get('container_height', 0)}px")

            self.log_signal.emit("=" * 60)

        except Exception as e:
            log.error(f"记录统计信息时发生错误: {str(e)}")
