#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack高精度单位转换器
解决测试模式和生产模式利用率差异问题
"""

import decimal
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Any, Tuple
import logging

# 配置日志
log = logging.getLogger(__name__)

class HighPrecisionConverter:
    """高精度单位转换器

    解决PPI转换过程中的精度损失问题，提高生产模式利用率
    """

    def __init__(self, ppi: int = 72):
        # 设置高精度计算上下文
        decimal.getcontext().prec = 28  # 28位精度
        decimal.getcontext().rounding = ROUND_HALF_UP

        self.ppi = Decimal(str(ppi))
        self.cm_to_inch = Decimal('0.393701')  # 高精度转换常数

        log.info(f"高精度转换器初始化: PPI={ppi}, 精度=28位")

    def cm_to_px_precise(self, cm_value: float) -> Decimal:
        """高精度cm转px转换

        Args:
            cm_value: 厘米值

        Returns:
            Decimal: 高精度像素值
        """
        cm_decimal = Decimal(str(cm_value))
        inches = cm_decimal * self.cm_to_inch
        pixels = inches * self.ppi
        return pixels

    def convert_canvas_precise(self, canvas_cm) -> Dict[str, Any]:
        """高精度画布转换

        Args:
            canvas_cm: CM单位画布结果

        Returns:
            Dict: 高精度PX画布数据
        """
        # 保持高精度直到最后
        width_px_precise = self.cm_to_px_precise(canvas_cm.width_cm)
        height_px_precise = self.cm_to_px_precise(canvas_cm.height_cm)

        placements_precise = []
        total_area_precise = Decimal('0')

        for placement_cm in canvas_cm.placements:
            x_precise = self.cm_to_px_precise(placement_cm.x_cm)
            y_precise = self.cm_to_px_precise(placement_cm.y_cm)
            width_precise = self.cm_to_px_precise(placement_cm.width_cm)
            height_precise = self.cm_to_px_precise(placement_cm.height_cm)

            placement_precise = {
                'x_precise': x_precise,
                'y_precise': y_precise,
                'width_precise': width_precise,
                'height_precise': height_precise,
                'rotated': placement_cm.rotated,
                'image_id': placement_cm.image_id,
                # 为PS调用准备的整数值（延迟舍入）
                'x_rounded': int(x_precise.quantize(Decimal('1'), rounding=ROUND_HALF_UP)),
                'y_rounded': int(y_precise.quantize(Decimal('1'), rounding=ROUND_HALF_UP)),
                'width_rounded': int(width_precise.quantize(Decimal('1'), rounding=ROUND_HALF_UP)),
                'height_rounded': int(height_precise.quantize(Decimal('1'), rounding=ROUND_HALF_UP))
            }

            placements_precise.append(placement_precise)
            total_area_precise += width_precise * height_precise

        # 计算高精度利用率
        canvas_area_precise = width_px_precise * height_px_precise
        utilization_precise = (total_area_precise / canvas_area_precise * 100) if canvas_area_precise > 0 else Decimal('0')

        # 计算传统方式的利用率作为对比
        width_px_traditional = int(float(width_px_precise))
        height_px_traditional = int(float(height_px_precise))
        total_area_traditional = Decimal('0')

        for placement_cm in canvas_cm.placements:
            width_traditional = int(self.cm_to_px_precise(placement_cm.width_cm))
            height_traditional = int(self.cm_to_px_precise(placement_cm.height_cm))
            total_area_traditional += Decimal(str(width_traditional * height_traditional))

        canvas_area_traditional = Decimal(str(width_px_traditional * height_px_traditional))
        utilization_traditional = (total_area_traditional / canvas_area_traditional * 100) if canvas_area_traditional > 0 else Decimal('0')

        # 计算精度改进
        precision_improvement = utilization_precise - utilization_traditional

        return {
            'canvas_index': canvas_cm.canvas_index,
            'width_px_precise': width_px_precise,
            'height_px_precise': height_px_precise,
            'width_px_rounded': int(width_px_precise.quantize(Decimal('1'), rounding=ROUND_HALF_UP)),
            'height_px_rounded': int(height_px_precise.quantize(Decimal('1'), rounding=ROUND_HALF_UP)),
            'placements_precise': placements_precise,
            'utilization_rate_precise': float(utilization_precise),
            'utilization_rate_traditional': float(utilization_traditional),
            'precision_improvement': float(precision_improvement),
            'total_area_precise': float(total_area_precise),
            'canvas_area_precise': float(canvas_area_precise),
            'precision_enhanced': True
        }

    def convert_results_precise(self, canvas_results_cm: List, ppi: int = None) -> List[Dict[str, Any]]:
        """转换多个画布结果（高精度）

        Args:
            canvas_results_cm: CM单位画布结果列表
            ppi: 可选的PPI值，如果提供则更新转换器PPI

        Returns:
            List[Dict]: 高精度PX画布结果列表
        """
        if ppi is not None and ppi != int(self.ppi):
            self.ppi = Decimal(str(ppi))
            log.info(f"更新PPI为: {ppi}")

        px_results = []
        total_utilization_precise = Decimal('0')
        total_canvas_count = 0

        for canvas_cm in canvas_results_cm:
            precise_result = self.convert_canvas_precise(canvas_cm)
            px_results.append(precise_result)

            total_utilization_precise += Decimal(str(precise_result['utilization_rate_precise']))
            total_canvas_count += 1

        # 计算整体利用率
        overall_utilization = float(total_utilization_precise / total_canvas_count) if total_canvas_count > 0 else 0.0

        log.info(f"高精度转换完成: {total_canvas_count}个画布, 整体利用率={overall_utilization:.2f}%")

        # 添加统计信息到第一个画布（如果存在）
        if px_results:
            px_results[0]['overall_utilization_precise'] = overall_utilization
            px_results[0]['total_canvas_count'] = total_canvas_count

        return px_results

    def create_error_compensation_data(self, placements_precise: List[Dict[str, Any]]) -> Dict[str, Any]:
        """创建误差补偿数据

        Args:
            placements_precise: 高精度放置数据

        Returns:
            Dict: 误差补偿数据
        """
        compensation_data = {
            'total_error_x': Decimal('0'),
            'total_error_y': Decimal('0'),
            'max_error_x': Decimal('0'),
            'max_error_y': Decimal('0'),
            'placement_errors': []
        }

        for placement in placements_precise:
            error_x = Decimal(str(placement['x_rounded'])) - placement['x_precise']
            error_y = Decimal(str(placement['y_rounded'])) - placement['y_precise']

            compensation_data['total_error_x'] += abs(error_x)
            compensation_data['total_error_y'] += abs(error_y)
            compensation_data['max_error_x'] = max(compensation_data['max_error_x'], abs(error_x))
            compensation_data['max_error_y'] = max(compensation_data['max_error_y'], abs(error_y))

            compensation_data['placement_errors'].append({
                'image_id': placement['image_id'],
                'error_x': float(error_x),
                'error_y': float(error_y)
            })

        # 转换为浮点数以便序列化
        compensation_data['total_error_x'] = float(compensation_data['total_error_x'])
        compensation_data['total_error_y'] = float(compensation_data['total_error_y'])
        compensation_data['max_error_x'] = float(compensation_data['max_error_x'])
        compensation_data['max_error_y'] = float(compensation_data['max_error_y'])

        return compensation_data

    def validate_precision_improvement(self, original_utilization: float,
                                     precise_utilization: float) -> Dict[str, Any]:
        """验证精度改进效果

        Args:
            original_utilization: 原始利用率
            precise_utilization: 高精度利用率

        Returns:
            Dict: 验证结果
        """
        improvement = precise_utilization - original_utilization
        improvement_percentage = (improvement / original_utilization * 100) if original_utilization > 0 else 0

        validation_result = {
            'original_utilization': original_utilization,
            'precise_utilization': precise_utilization,
            'improvement': improvement,
            'improvement_percentage': improvement_percentage,
            'is_improved': improvement > 0,
            'significant_improvement': improvement > 1.0  # 改进超过1%认为显著
        }

        log.info(f"精度改进验证: 原始={original_utilization:.2f}%, "
                f"高精度={precise_utilization:.2f}%, 改进={improvement:.2f}%")

        return validation_result


class ErrorCompensatedPSProcessor:
    """误差补偿PS处理器

    在PS图片放置过程中补偿累积误差，提高最终利用率
    """

    def __init__(self):
        self.cumulative_error_x = Decimal('0')
        self.cumulative_error_y = Decimal('0')
        self.placement_count = 0
        self.error_history = []

        log.info("误差补偿PS处理器初始化")

    def reset_error_compensation(self):
        """重置误差补偿状态"""
        self.cumulative_error_x = Decimal('0')
        self.cumulative_error_y = Decimal('0')
        self.placement_count = 0
        self.error_history.clear()
        log.info("误差补偿状态已重置")

    def place_image_with_compensation(self, placement_precise: Dict[str, Any],
                                    image_path: str, **kwargs) -> bool:
        """带误差补偿的图片放置

        Args:
            placement_precise: 高精度放置数据
            image_path: 图片路径
            **kwargs: 其他PS参数

        Returns:
            bool: 放置是否成功
        """
        try:
            # 计算补偿后的坐标
            target_x = placement_precise['x_precise'] - self.cumulative_error_x
            target_y = placement_precise['y_precise'] - self.cumulative_error_y

            # 舍入到整数
            rounded_x = int(target_x.quantize(Decimal('1'), rounding=ROUND_HALF_UP))
            rounded_y = int(target_y.quantize(Decimal('1'), rounding=ROUND_HALF_UP))

            # 计算实际误差
            actual_error_x = Decimal(str(rounded_x)) - target_x
            actual_error_y = Decimal(str(rounded_y)) - target_y

            # 更新累积误差
            self.cumulative_error_x += actual_error_x
            self.cumulative_error_y += actual_error_y

            # 记录误差历史
            self.error_history.append({
                'placement_index': self.placement_count,
                'image_id': placement_precise['image_id'],
                'target_x': float(target_x),
                'target_y': float(target_y),
                'rounded_x': rounded_x,
                'rounded_y': rounded_y,
                'error_x': float(actual_error_x),
                'error_y': float(actual_error_y),
                'cumulative_error_x': float(self.cumulative_error_x),
                'cumulative_error_y': float(self.cumulative_error_y)
            })

            # 调用PS放置
            from utils.photoshop_helper import PhotoshopHelper

            success = PhotoshopHelper.place_image(
                image_path=image_path,
                x=rounded_x,
                y=rounded_y,
                width=placement_precise['width_rounded'],
                height=placement_precise['height_rounded'],
                rotation=kwargs.get('rotation', 0),
                ppi=kwargs.get('ppi', 72),
                layer_index=kwargs.get('layer_index', self.placement_count),
                total_images=kwargs.get('total_images', 1),
                layer_name=kwargs.get('layer_name', f"Layer_{self.placement_count}"),
                unique_id=kwargs.get('unique_id', placement_precise['image_id'])
            )

            self.placement_count += 1

            if success:
                log.debug(f"误差补偿放置成功: {placement_precise['image_id']} "
                         f"at ({rounded_x},{rounded_y}), 累积误差=({float(self.cumulative_error_x):.3f},"
                         f"{float(self.cumulative_error_y):.3f})")
            else:
                log.warning(f"误差补偿放置失败: {placement_precise['image_id']}")

            return success

        except Exception as e:
            log.error(f"误差补偿PS处理失败: {str(e)}")
            return False

    def get_error_statistics(self) -> Dict[str, Any]:
        """获取误差统计信息

        Returns:
            Dict: 误差统计
        """
        if not self.error_history:
            return {'no_data': True}

        errors_x = [entry['error_x'] for entry in self.error_history]
        errors_y = [entry['error_y'] for entry in self.error_history]

        stats = {
            'total_placements': len(self.error_history),
            'final_cumulative_error_x': float(self.cumulative_error_x),
            'final_cumulative_error_y': float(self.cumulative_error_y),
            'average_error_x': sum(abs(e) for e in errors_x) / len(errors_x),
            'average_error_y': sum(abs(e) for e in errors_y) / len(errors_y),
            'max_error_x': max(abs(e) for e in errors_x),
            'max_error_y': max(abs(e) for e in errors_y),
            'error_reduction_x': abs(float(self.cumulative_error_x)) < sum(abs(e) for e in errors_x),
            'error_reduction_y': abs(float(self.cumulative_error_y)) < sum(abs(e) for e in errors_y)
        }

        log.info(f"误差统计: 总放置={stats['total_placements']}, "
                f"平均误差=({stats['average_error_x']:.3f},{stats['average_error_y']:.3f}), "
                f"最终累积误差=({stats['final_cumulative_error_x']:.3f},"
                f"{stats['final_cumulative_error_y']:.3f})")

        return stats


def create_precision_enhanced_converter(ppi: int = 72) -> HighPrecisionConverter:
    """创建精度增强转换器的便捷函数

    Args:
        ppi: 像素密度

    Returns:
        HighPrecisionConverter: 高精度转换器实例
    """
    return HighPrecisionConverter(ppi)


def create_error_compensated_processor() -> ErrorCompensatedPSProcessor:
    """创建误差补偿处理器的便捷函数

    Returns:
        ErrorCompensatedPSProcessor: 误差补偿处理器实例
    """
    return ErrorCompensatedPSProcessor()


# 测试函数
def test_precision_converter():
    """测试高精度转换器"""
    print("测试高精度转换器")
    print("=" * 30)

    converter = HighPrecisionConverter(72)

    # 测试单个值转换
    test_cm = 163.5
    precise_px = converter.cm_to_px_precise(test_cm)
    print(f"测试转换: {test_cm}cm -> {precise_px}px (高精度)")
    print(f"传统转换: {test_cm}cm -> {int(test_cm * 72 / 2.54)}px (整数)")

    # 测试精度差异
    traditional_px = int(test_cm * 72 / 2.54)
    precise_float = float(precise_px)
    difference = abs(precise_float - traditional_px)
    print(f"精度差异: {difference:.6f}px")

    print("\n✅ 高精度转换器测试完成")


if __name__ == "__main__":
    test_precision_converter()
