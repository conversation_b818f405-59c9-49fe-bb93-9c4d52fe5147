# Supabase心跳同步功能使用说明

## 概述

Supabase心跳同步功能是一个基于第一性原理设计的模块化系统，负责定期从Supabase云端同步配置参数，确保应用程序始终使用最新的配置。

## 设计原则

本功能严格遵循以下设计原则：

1. **DRY原则（Don't Repeat Yourself）**：避免重复代码，将同步逻辑集中在独立的管理器中
2. **KISS原则（Keep It Simple, Stupid）**：保持简单直观的API设计
3. **SOLID原则**：单一职责、开闭原则、接口隔离等
4. **YAGNI原则（You Aren't Gonna Need It）**：只实现当前需要的功能

## 核心组件

### 1. HeartRateSyncManager（心跳同步管理器）

负责管理心跳同步的核心逻辑：

```python
from utils.heart_rate_sync_manager import HeartRateSyncManager

# 创建管理器实例
sync_manager = HeartRateSyncManager(
    config_manager=config_manager,
    supabase_helper=supabase_helper,
    log_callback=log_function  # 可选的日志回调
)

# 初始化心跳同步
success = sync_manager.initialize()

# 获取同步状态
status = sync_manager.get_status()

# 强制执行一次同步
sync_manager.force_sync()

# 停止心跳同步
sync_manager.stop()
```

### 2. 配置参数

心跳同步功能使用以下配置参数：

- `is_heart_rate`：是否启用心跳同步（布尔值）
- `heart_rate_time`：心跳间隔时间（秒，范围：30-86400）

## 功能特性

### 1. 自动同步

- **当 `is_heart_rate = TRUE` 时**：
  - 程序启动后自动获取一次Supabase参数
  - 启动定期心跳同步，根据`heart_rate_time`参数设置同步间隔
  - 在程序生命周期内持续进行定期同步

- **当 `is_heart_rate = FALSE` 时**：
  - 程序启动时执行一次性Supabase参数同步
  - 不启动定时器，在程序生命周期内不再进行同步
  - 确保程序启动时获取最新配置，但避免不必要的网络请求

### 2. 错误处理与恢复

- 连续错误检测：最多允许3次连续同步失败
- 自动暂停：超过错误限制时自动暂停同步
- 自动恢复：暂停10分钟后尝试恢复，失败则30分钟后重试

### 3. 智能日志

- 减少日志噪音：成功同步时只在首次或恢复后显示日志
- 关键错误记录：重点记录影响功能的关键错误
- 状态监控：提供详细的同步状态信息

### 4. 信号机制

管理器提供以下PyQt信号：

```python
# 连接信号
sync_manager.sync_started.connect(on_sync_started)
sync_manager.sync_completed.connect(on_sync_completed)  # 参数：bool success
sync_manager.sync_error.connect(on_sync_error)  # 参数：str error_message
sync_manager.sync_paused.connect(on_sync_paused)  # 参数：str reason
sync_manager.sync_resumed.connect(on_sync_resumed)
```

## 同步内容

心跳同步会同步以下配置项（按重要性排序）：

1. **基础配置**：核心应用配置
2. **心跳同步设置**：心跳同步本身的参数
3. **表格模式**：标准/自定义表格模式
4. **模糊查询**：模糊查询开关
5. **图库索引快速模式**：图库索引模式
6. **测试模式**：测试模式开关
7. **高级设置密码**：非关键操作

## 使用示例

### 在主应用程序中使用

```python
class ImageLayoutApp(QMainWindow):
    def __init__(self):
        super().__init__()

        # 初始化心跳同步管理器
        self._initialize_heart_rate_sync_manager()

    def _initialize_heart_rate_sync_manager(self):
        """初始化Supabase心跳同步管理器"""
        from utils.heart_rate_sync_manager import HeartRateSyncManager

        # 创建管理器实例
        self.heart_rate_sync_manager = HeartRateSyncManager(
            config_manager=self.config_manager,
            supabase_helper=self.supabase_helper,
            log_callback=self.add_log
        )

        # 连接信号
        self.heart_rate_sync_manager.sync_completed.connect(self._on_sync_completed)

        # 初始化
        self.heart_rate_sync_manager.initialize()

    def _on_sync_completed(self, success):
        """同步完成事件处理"""
        if success:
            # 同步成功，可以更新UI状态
            pass
        else:
            # 同步失败，可以显示警告
            pass

    def closeEvent(self, event):
        """应用程序关闭时停止心跳同步"""
        if hasattr(self, 'heart_rate_sync_manager'):
            self.heart_rate_sync_manager.stop()
        super().closeEvent(event)
```

### 配置心跳同步参数

```python
# 启用心跳同步，间隔5分钟（定期同步）
config_manager.set('is_heart_rate', True)
config_manager.set('heart_rate_time', 300)

# 禁用心跳同步（仅一次性同步）
config_manager.set('is_heart_rate', False)

# 获取当前设置
settings = config_manager.get_heart_rate_settings()
print(f"心跳同步: {settings['is_heart_rate']}")
print(f"间隔时间: {settings['heart_rate_time']}秒")

# 注意：
# - 当 is_heart_rate = True 时：程序启动后会定期同步
# - 当 is_heart_rate = False 时：程序启动时只同步一次，之后不再同步
```

## 状态监控

获取详细的同步状态信息：

```python
status = sync_manager.get_status()

# 状态信息包含：
# - is_enabled: 是否启用
# - interval_seconds: 间隔时间（秒）
# - is_paused: 是否暂停
# - last_sync_time: 最后同步时间
# - sync_error_count: 连续错误次数
# - max_sync_errors: 最大允许错误次数
# - timer_active: 定时器是否活跃
```

## 测试

运行测试脚本验证功能：

```bash
python test_heart_rate_sync.py
```

测试脚本会验证：
- 配置管理器的心跳同步设置
- 心跳同步管理器的基本功能
- 信号机制的正确性
- 错误处理和恢复机制

## 注意事项

1. **网络依赖**：心跳同步需要网络连接和Supabase认证
2. **资源消耗**：合理设置同步间隔，避免过于频繁的网络请求
3. **错误处理**：应用程序应该能够在心跳同步失败时正常运行
4. **线程安全**：所有操作都在主线程中执行，使用PyQt的信号槽机制

## 故障排除

### 常见问题

1. **心跳同步不工作**
   - 检查`is_heart_rate`参数是否为True
   - 检查网络连接和Supabase认证状态
   - 查看日志中的错误信息

2. **同步频率过高或过低**
   - 检查`heart_rate_time`参数设置
   - 注意参数会被自动调整到30-86400秒范围内

3. **同步暂停**
   - 检查是否有连续的网络错误
   - 等待自动恢复或重启应用程序

### 日志分析

关键日志信息：
- `✅ Supabase心跳同步已启动`：同步成功启动
- `⚠️ 心跳同步已暂停`：同步因错误暂停
- `🔄 尝试恢复心跳同步`：正在尝试恢复
- `❌ 关键同步失败`：重要配置同步失败

## 版本历史

- v1.0.0：初始版本，基本心跳同步功能
- v1.1.0：添加错误恢复机制和信号支持
- v1.2.0：优化日志记录和状态监控
