#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片处理器模块，提供统一的图片处理接口，分离测试模式和正式模式的实现
"""

import os
import sys
import random
import logging
import datetime
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Tuple, Optional, Union

# 导入时间处理工具，避免time变量作用域问题
from utils.time_helper import get_timestamp

# 导入常量
from utils.constants import constants, get_constant

# 配置日志
from utils.log_config import get_logger
log = get_logger("ImageProcessor")

# 导入PIL库，用于测试模式下生成色块图片
try:
    from PIL import Image, ImageDraw, ImageFont
except ImportError:
    # 如果没有安装PIL库，尝试安装
    import subprocess
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pillow"])
        from PIL import Image, ImageDraw, ImageFont
    except Exception as e:
        log.error(f"安装PIL库失败: {str(e)}")
        # 定义一个空的Image类，避免导入错误
        class Image:
            @staticmethod
            def new(*args, **kwargs):
                return None

        class ImageDraw:
            @staticmethod
            def Draw(*args, **kwargs):
                return None

        class ImageFont:
            @staticmethod
            def truetype(*args, **kwargs):
                return None


class ImageProcessorBase(ABC):
    """图片处理器基类，定义了图片处理的通用接口"""

    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化处理器

        Args:
            config: 配置参数

        Returns:
            是否初始化成功
        """
        pass

    @abstractmethod
    def create_canvas(self, width: int, height: int, name: str, ppi: int = 72) -> bool:
        """创建画布

        Args:
            width: 画布宽度（像素）
            height: 画布高度（像素）
            name: 画布名称
            ppi: 画布分辨率（像素/英寸）

        Returns:
            是否创建成功
        """
        pass

    @abstractmethod
    def place_image(self, image_info: Dict[str, Any]) -> bool:
        """放置图片

        Args:
            image_info: 图片信息，包含路径、位置、尺寸等

        Returns:
            是否放置成功
        """
        pass

    @abstractmethod
    def save_canvas(self, output_path: str) -> bool:
        """保存画布

        Args:
            output_path: 输出路径

        Returns:
            是否保存成功
        """
        pass

    @abstractmethod
    def close_canvas(self) -> bool:
        """关闭画布

        Returns:
            是否关闭成功
        """
        pass

    @abstractmethod
    def cleanup(self) -> bool:
        """清理资源

        Returns:
            是否清理成功
        """
        pass

    @abstractmethod
    def generate_description(self, output_path: str, images_info: List[Dict[str, Any]],
                            canvas_info: Dict[str, Any]) -> bool:
        """生成画布说明文档

        Args:
            output_path: 输出路径
            images_info: 图片信息列表
            canvas_info: 画布信息

        Returns:
            是否生成成功
        """
        pass


class PhotoshopImageProcessor(ImageProcessorBase):
    """Photoshop图片处理器，使用Photoshop API处理图片"""

    def __init__(self):
        """初始化Photoshop图片处理器"""
        self.initialized = False
        self.ps_helper = None

    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化处理器

        Args:
            config: 配置参数

        Returns:
            是否初始化成功
        """
        try:
            # 导入PhotoshopHelper
            from utils.photoshop_helper import PhotoshopHelper
            self.ps_helper = PhotoshopHelper

            # 检查Photoshop是否可用
            is_available, message = self.ps_helper.check_photoshop()
            if not is_available:
                log.error(f"Photoshop不可用: {message}")
                return False

            self.initialized = True
            log.info("Photoshop图片处理器初始化成功")
            return True
        except Exception as e:
            log.error(f"初始化Photoshop图片处理器失败: {str(e)}")
            return False

    def create_canvas(self, width: int, height: int, name: str, ppi: int = 72) -> bool:
        """创建画布

        Args:
            width: 画布宽度（像素）
            height: 画布高度（像素）
            name: 画布名称
            ppi: 画布分辨率（像素/英寸）

        Returns:
            是否创建成功
        """
        if not self.initialized:
            log.error("Photoshop图片处理器未初始化")
            return False

        try:
            # 检查画布尺寸是否超过最大限制
            max_width_px = get_constant('PS_MAX_CANVAS_WIDTH_PX', 35433)  # 默认3米
            max_height_px = get_constant('PS_MAX_CANVAS_HEIGHT_PX', 590551)  # 默认50米

            if width > max_width_px:
                log.warning(f"画布宽度 {width}px 超过最大限制 {max_width_px}px，将被截断")
                width = max_width_px

            if height > max_height_px:
                log.warning(f"画布高度 {height}px 超过最大限制 {max_height_px}px，将被截断")
                height = max_height_px

            # 使用默认PPI如果未指定
            if ppi <= 0:
                ppi = get_constant('PS_DEFAULT_PPI', 72)

            return self.ps_helper.create_canvas(width, height, name, ppi)
        except Exception as e:
            log.error(f"创建画布失败: {str(e)}")
            return False

    def place_image(self, image_info: Dict[str, Any]) -> bool:
        """放置图片 - 严格按照RectPack算法的布局结果

        Args:
            image_info: 图片信息，包含路径、位置、尺寸等

        Returns:
            是否放置成功
        """
        if not self.initialized:
            log.error("Photoshop图片处理器未初始化")
            return False

        try:
            # 获取RectPack算法的精确布局信息
            image_path = image_info.get('image_path', '')
            x = image_info.get('x', 0)  # RectPack算法计算的精确坐标
            y = image_info.get('y', 0)
            width = image_info.get('width', 0)  # RectPack算法计算的精确尺寸
            height = image_info.get('height', 0)
            rotated = image_info.get('rotated', False)  # RectPack的旋转决策
            image_name = image_info.get('name', os.path.basename(image_path))

            # 记录RectPack布局信息 - 添加详细日志用于调试
            log.info(f"📍 RectPack算法布局: {image_name}")
            log.info(f"  • 坐标: ({x}, {y}) 像素")
            log.info(f"  • 尺寸: {width}x{height} 像素")
            log.info(f"  • 旋转: {rotated} ({'90度' if rotated else '无旋转'})")
            log.info(f"  • 路径: {image_path}")

            # 添加旋转调试信息
            if 'rotation_debug' in image_info:
                debug_info = image_info['rotation_debug']
                log.info(f"  • 旋转调试信息:")
                log.info(f"    - 原始尺寸: {debug_info.get('original_size', 'N/A')}")
                log.info(f"    - 放置尺寸: {debug_info.get('placed_size', 'N/A')}")
                log.info(f"    - 是否正方形: {debug_info.get('is_square', False)}")
                log.info(f"    - 接近正方形: {debug_info.get('is_square_like', False)}")
                log.info(f"    - 原始宽高比: {debug_info.get('aspect_ratio_original', 'N/A')}")
                log.info(f"    - 放置宽高比: {debug_info.get('aspect_ratio_placed', 'N/A')}")
                log.info(f"    - RectPack旋转: {debug_info.get('rectpack_rotated', False)}")
                log.info(f"    - 方向改变: {debug_info.get('orientation_changed', False)}")
                log.info(f"    - 原始横向: {debug_info.get('original_is_landscape', False)}")
                log.info(f"    - 放置横向: {debug_info.get('placed_is_landscape', False)}")
                log.info(f"    - 尺寸已交换: {debug_info.get('size_swapped', False)}")

            # 验证坐标和尺寸的合理性
            if x < 0 or y < 0:
                log.warning(f"  ⚠️ 警告: 坐标为负数 ({x}, {y})")
            if width <= 0 or height <= 0:
                log.warning(f"  ⚠️ 警告: 尺寸无效 ({width}x{height})")
            if x == 0 and y == 0:
                log.info(f"  📍 注意: 图片放置在左上角 (0,0) 位置")

            # 使用RectPack算法的精确布局参数调用PhotoshopHelper
            # 重要：直接传递RectPack算法计算出的精确坐标和尺寸，不做任何修改
            rotation_angle = 90 if rotated else 0

            # 获取PPI设置（使用统一的单位转换器）
            ppi = 72  # 默认PPI
            try:
                from utils.unit_converter import get_global_ppi
                ppi = get_global_ppi()
            except Exception as e:
                log.warning(f"获取全局PPI失败，使用默认值72: {str(e)}")

            # 获取唯一标识符和图层信息（优化版本）
            unique_id = image_info.get('unique_id', f"{image_name}_{x}_{y}_{int(get_timestamp() * 1000)}")
            layer_index = image_info.get('layer_index', 0)
            total_images = image_info.get('total_images', 1)
            layer_name = image_info.get('layer_name', f"{image_name}_{unique_id}")

            # 使用RectPack算法的精确布局参数调用PhotoshopHelper
            # 重要：直接传递RectPack算法计算出的精确坐标和尺寸，不做任何修改
            # 优化版本：添加唯一标识符和图层信息以避免重名冲突
            success = self.ps_helper.place_image(
                image_path=image_path,
                x=x,  # RectPack算法的精确坐标，不做任何转换
                y=y,  # RectPack算法的精确坐标，不做任何转换
                width=width,  # RectPack算法的精确尺寸，不做任何转换
                height=height,  # RectPack算法的精确尺寸，不做任何转换
                rotation=rotation_angle,  # RectPack的旋转决策
                ppi=ppi,  # 从全局单位转换器读取的PPI
                layer_index=layer_index,  # 图层索引
                total_images=total_images,  # 总图片数
                layer_name=layer_name,  # 唯一图层名称
                unique_id=unique_id  # 唯一标识符
            )

            if success:
                log.info(f"RectPack布局成功: {image_name} at ({x},{y}) {width}x{height}px")
                if rotation_angle > 0:
                    log.info(f"  旋转角度: {rotation_angle}°")
            else:
                log.error(f"RectPack布局失败: {image_name} at ({x},{y}) {width}x{height}px")

            return success

        except Exception as e:
            log.error(f"放置图片失败: {str(e)}")
            return False

    def save_canvas(self, output_path: str) -> bool:
        """保存画布

        Args:
            output_path: 输出路径

        Returns:
            是否保存成功
        """
        if not self.initialized:
            log.error("Photoshop图片处理器未初始化")
            return False

        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 根据文件扩展名确定保存格式
            ext = os.path.splitext(output_path)[1].lower()
            format = 'JPEG' if ext in ['.jpg', '.jpeg'] else 'TIFF'

            return self.ps_helper.save_document(output_path, format)
        except Exception as e:
            log.error(f"保存画布失败: {str(e)}")
            return False

    def close_canvas(self, save: bool = False) -> bool:
        """关闭画布以节省内存

        Args:
            save: 是否在关闭前保存

        Returns:
            是否关闭成功
        """
        if not self.initialized:
            log.error("Photoshop图片处理器未初始化")
            return False

        try:
            log.info(f"关闭画布以节省内存 (保存: {save})")
            return self.ps_helper.close_document(save=save)
        except Exception as e:
            log.error(f"关闭画布失赅: {str(e)}")
            return False

    def cleanup(self) -> bool:
        """清理资源

        Returns:
            是否清理成功
        """
        if not self.initialized:
            return True  # 未初始化，无需清理

        try:
            return self.ps_helper.safe_cleanup_resources()
        except Exception as e:
            log.error(f"清理资源失败: {str(e)}")
            return False

    def generate_description(self, output_path: str, images_info: List[Dict[str, Any]],
                            canvas_info: Dict[str, Any]) -> bool:
        """生成画布说明文档

        Args:
            output_path: 输出路径
            images_info: 图片信息列表
            canvas_info: 画布信息

        Returns:
            是否生成成功
        """
        try:
            # 从 TIFF 文件路径生成说明文档路径 - 只生成TXT格式
            tiff_filename = os.path.basename(output_path)
            base_name = os.path.splitext(tiff_filename)[0]  # 去掉.tif扩展名
            output_dir = os.path.dirname(output_path)
            doc_path = os.path.join(output_dir, f"{base_name}_说明.txt")

            log.info(f"生成图像排列算法说明文档: {doc_path}")

            # 确保输出目录存在
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 提取画布信息 - 完全参照tetris算法的数据结构
            material_name = canvas_info.get('material_name', '')
            canvas_sequence = canvas_info.get('canvas_sequence', 0)
            canvas_width_m = canvas_info.get('canvas_width_m', 0)
            canvas_width_px = canvas_info.get('canvas_width_px', 0)
            canvas_height = canvas_info.get('canvas_height', 0)
            horizontal_expansion_cm = canvas_info.get('horizontal_expansion_cm', 0)
            max_height_cm = canvas_info.get('max_height_cm', 0)
            ppi = canvas_info.get('ppi', 72)
            generation_time = canvas_info.get('generation_time', '')

            # 计算画布高度（厘米）
            canvas_height_cm = canvas_height/ppi*2.54

            # 统计信息
            total_images = len(images_info)
            class_a_count = len([img for img in images_info if img.get('image_class') == 'A'])
            class_b_count = len([img for img in images_info if img.get('image_class') == 'B'])
            class_c_count = len([img for img in images_info if img.get('image_class') == 'C'])
            rotated_images = len([img for img in images_info if img.get('need_rotation', False) or img.get('rotated', False)])

            # 计算利用率
            if canvas_width_px > 0 and canvas_height > 0:
                total_area = canvas_width_px * canvas_height
                used_area = sum([img.get('width', 0) * img.get('height', 0) for img in images_info])
                utilization = used_area / total_area if total_area > 0 else 0
            else:
                utilization = 0

            # 获取当前时间
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 生成说明文档内容 - 使用用户友好的表述
            with open(doc_path, 'w', encoding='utf-8') as f:
                # 标题和基本信息
                f.write(f"{material_name}-{canvas_sequence} 生产模式报告\n")
                f.write(f"生成时间: {current_time}\n")
                f.write("★ 基本信息\n")
                f.write(f"材质名称: {material_name}\n")
                f.write(f"画布序号: {canvas_sequence}\n")
                f.write("算法类型: 图像排列算法\n")
                f.write("生产模式: 启用 (Photoshop集成)\n")

                # 容器详情
                f.write("★ 容器详情\n")
                f.write(f"基础宽度: {int(canvas_width_m*100)}cm\n")
                f.write(f"水平拓展: {horizontal_expansion_cm}cm\n")
                f.write(f"实际宽度: {int(canvas_width_m*100)}cm\n")
                f.write(f"最大高度: {max_height_cm}cm\n")
                f.write(f"实际高度: {canvas_height_cm:.0f}cm\n")
                f.write("单位转换: cm直接转px (生产模式)\n")

                # 布局统计信息
                f.write("★ 布局统计\n")
                f.write(f"画布利用率: {utilization*100:.2f}%\n")
                f.write(f"旋转图片比例: {rotated_images/total_images*100:.2f}% ({rotated_images}/{total_images})\n")

                f.write(f"成功放置图片: {total_images}张\n")
                f.write(f"旋转图片数: {rotated_images}张\n")

                # 计算利用率评价
                if utilization >= 0.85:
                    rating = "★★★★★ 优秀"
                elif utilization >= 0.75:
                    rating = "★★★★☆ 良好"
                elif utilization >= 0.65:
                    rating = "★★★☆☆ 中等"
                elif utilization >= 0.50:
                    rating = "★★☆☆☆ 较差"
                else:
                    rating = "★☆☆☆☆ 待优化"

                f.write(f"利用率评价: {rating}\n")

                # 详细图片信息
                f.write("★ 详细图片信息\n")
                f.write("| 序号 | 图片标识 | 尺寸(px) | 位置(x,y) | 尺寸(cm) | 旋转 | 面积(px²) |\n")
                f.write("|------|----------|----------|-----------|----------|------|-----------||\n")

                # 按Y坐标排序，使得输出更有序
                sorted_images = sorted(images_info, key=lambda img: (img.get('y', 0), img.get('x', 0)))

                for i, img in enumerate(sorted_images, 1):
                    name = img.get('name', f'Image_{i}')
                    width = img.get('width', 0)
                    height = img.get('height', 0)
                    x = img.get('x', 0)
                    y = img.get('y', 0)
                    rotated = '是' if (img.get('need_rotation', False) or img.get('rotated', False)) else '否'
                    area = width * height

                    # 使用表格中的厘米尺寸数据，避免单位换算误差
                    width_cm = img.get('width_cm', img.get('original_width_cm', 0))
                    height_cm = img.get('height_cm', img.get('original_height_cm', 0))
                    size_cm = f"{width_cm:.1f}x{height_cm:.1f}"

                    f.write(f"| {i} | {name} | {width}x{height} | ({x},{y}) | {size_cm} | {rotated} | {area:,} |\n")

                # 算法参数
                f.write("★ 算法参数\n")
                f.write("排序策略: 按面积排序\n")
                f.write("装箱算法: 智能排列算法\n")
                f.write("旋转功能: 启用\n")
                f.write("间距处理: 统一间距\n")
                f.write("优化目标: 最大化空间利用率\n")

            log.info(f"图像排列算法说明文档生成成功: {os.path.basename(doc_path)}")
            return True
        except Exception as e:
            log.error(f"生成图像排列算法说明文档失败: {str(e)}")
            return False


class TestModeImageProcessor(ImageProcessorBase):
    """测试模式图片处理器，使用PIL库生成色块图片"""

    def __init__(self):
        """初始化测试模式图片处理器"""
        self.initialized = False
        self.canvas = None
        self.draw = None
        self.config = {}
        self.is_test_all_data = False  # 默认不测试全部数据
        self.canvas_width = 0
        self.canvas_height = 0
        self.canvas_name = ""

    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化处理器

        Args:
            config: 配置参数，包含is_test_all_data

        Returns:
            是否初始化成功
        """
        try:
            # 保存配置
            self.config = config

            # 提取测试模式参数
            self.is_test_all_data = config.get('is_test_all_data', False)

            # 检查PIL库是否可用
            try:
                from PIL import Image, ImageDraw, ImageFont
                self.initialized = True
                log.info(f"测试模式图片处理器初始化成功，测试全部数据: {self.is_test_all_data}")
                log.info("测试模式使用cm直接转px的方式实现缩小模型")
                return True
            except ImportError:
                log.error("PIL库不可用，测试模式无法正常工作")
                return False
        except Exception as e:
            log.error(f"初始化测试模式图片处理器失败: {str(e)}")
            return False

    def create_canvas(self, width: int, height: int, name: str, ppi: int = 72) -> bool:
        """创建画布

        Args:
            width: 画布宽度（像素）
            height: 画布高度（像素）
            name: 画布名称
            ppi: 画布分辨率（像素/英寸）

        Returns:
            是否创建成功
        """
        if not self.initialized:
            log.error("测试模式图片处理器未初始化")
            return False

        try:
            # 检查画布尺寸是否超过最大限制
            max_width_px = get_constant('PS_MAX_CANVAS_WIDTH_PX', 35433)  # 默认3米
            max_height_px = get_constant('PS_MAX_CANVAS_HEIGHT_PX', 590551)  # 默认50米

            if width > max_width_px:
                log.warning(f"画布宽度 {width}px 超过最大限制 {max_width_px}px，将被截断")
                width = max_width_px

            if height > max_height_px:
                log.warning(f"画布高度 {height}px 超过最大限制 {max_height_px}px，将被截断")
                height = max_height_px

            # 测试模式使用cm直接转px的方式，不再使用缩小比率
            # 画布尺寸已经通过cm转px的方式处理，直接使用传入的尺寸
            scaled_width = width
            scaled_height = height

            # 测试模式下限制画布大小，防止内存溢出
            MAX_TEST_CANVAS_HEIGHT = 30000  # 最大高度限制为30000像素
            MAX_TEST_CANVAS_WIDTH = 20000   # 最大宽度限制为20000像素

            # 检查画布尺寸是否过大
            if scaled_height > MAX_TEST_CANVAS_HEIGHT or scaled_width > MAX_TEST_CANVAS_WIDTH:
                log.warning(f"画布尺寸过大 ({scaled_width}x{scaled_height})，将进行进一步缩放")

                # 计算缩放比例
                scale_factor = min(
                    MAX_TEST_CANVAS_HEIGHT / scaled_height if scaled_height > 0 else 1,
                    MAX_TEST_CANVAS_WIDTH / scaled_width if scaled_width > 0 else 1
                )

                # 缩放画布尺寸
                scaled_width = int(scaled_width * scale_factor)
                scaled_height = int(scaled_height * scale_factor)

                log.info(f"画布已缩放: 原始尺寸={width}x{height}, 缩放比例={scale_factor:.6f}, 新尺寸={scaled_width}x{scaled_height}")
            else:
                log.info(f"测试模式画布: 原始尺寸={width}x{height}, 新尺寸={scaled_width}x{scaled_height} (cm直接转px)")

            # 创建画布 - 明确使用RGB模式
            from PIL import Image, ImageDraw
            self.canvas = Image.new('RGB', (scaled_width, scaled_height), (255, 255, 255))
            self.draw = ImageDraw.Draw(self.canvas)

            # 保存画布信息
            self.canvas_width = width  # 保存原始宽度
            self.canvas_height = height  # 保存原始高度
            self.canvas_name = name

            log.info(f"测试模式画布 {name} 创建成功，尺寸: {scaled_width}x{scaled_height}")
            return True
        except Exception as e:
            log.error(f"创建测试模式画布失败: {str(e)}")
            return False

    def place_image(self, image_info: Dict[str, Any]) -> bool:
        """放置图片

        Args:
            image_info: 图片信息，包含路径、位置、尺寸等

        Returns:
            是否放置成功
        """
        if not self.initialized or self.canvas is None or self.draw is None:
            log.error("测试模式画布未创建")
            return False

        try:
            # 提取图片信息
            image_path = image_info.get('image_path', '')
            image_name = image_info.get('name', os.path.basename(image_path))
            x = image_info.get('x', 0)
            y = image_info.get('y', 0)
            width = image_info.get('width', 0)
            height = image_info.get('height', 0)
            rotated = image_info.get('rotated', False)
            image_class = image_info.get('image_class', 'C')

            # 记录旋转信息
            if rotated:
                log.info(f"测试模式处理旋转图片: {image_name}, 尺寸: {width}x{height}，已包含旋转后的正确尺寸")

            # 测试模式使用cm直接转px的方式，不再使用缩小比率
            # 坐标和尺寸已经通过cm转px的方式处理，直接使用传入的值
            scaled_x = x
            scaled_y = y
            scaled_width = width
            scaled_height = height

            # 确保坐标和尺寸在有效范围内
            canvas_size = self.canvas.size
            scaled_x = max(0, min(scaled_x, canvas_size[0] - 1))
            scaled_y = max(0, min(scaled_y, canvas_size[1] - 1))
            scaled_width = max(1, min(scaled_width, canvas_size[0] - scaled_x))
            scaled_height = max(1, min(scaled_height, canvas_size[1] - scaled_y))

            # 根据图片类别设置颜色
            if image_class == 'A':
                # A类图片 - 红色系
                color = (random.randint(150, 250), random.randint(0, 100), random.randint(0, 100))
            elif image_class == 'B':
                # B类图片 - 绿色系
                color = (random.randint(0, 100), random.randint(150, 250), random.randint(0, 100))
            else:  # C类
                # C类图片 - 蓝色系
                color = (random.randint(0, 100), random.randint(0, 100), random.randint(150, 250))

            # 绘制矩形
            self.draw.rectangle(
                [scaled_x, scaled_y, scaled_x + scaled_width, scaled_y + scaled_height],
                outline=(0, 0, 0),
                fill=color
            )

            # 绘制图片名称
            try:
                from PIL import ImageFont
                try:
                    font = ImageFont.truetype("arial.ttf", 12)
                except:
                    font = ImageFont.load_default()

                # 绘制图片名称 - 只有当矩形足够大时才绘制
                if scaled_width > 30 and scaled_height > 15:
                    display_name = f"{image_name}{'(R)' if rotated else ''}"
                    self.draw.text((scaled_x + 5, scaled_y + 5), display_name, fill=(255, 255, 255), font=font)
            except Exception as font_error:
                log.warning(f"绘制图片名称失败: {str(font_error)}")

            log.info(f"测试模式下放置图片 {image_name} 成功，位置: ({scaled_x}, {scaled_y}), 尺寸: {scaled_width}x{scaled_height}, 类别: {image_class}, 旋转: {rotated}")
            return True
        except Exception as e:
            log.error(f"测试模式下放置图片失败: {str(e)}")
            return False

    def save_canvas(self, output_path: str) -> bool:
        """保存画布

        Args:
            output_path: 输出路径

        Returns:
            是否保存成功
        """
        if not self.initialized or self.canvas is None:
            log.error("测试模式画布未创建")
            return False

        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 保存画布
            self.canvas.save(output_path)

            log.info(f"测试模式画布已保存到: {output_path}")
            return True
        except Exception as e:
            log.error(f"保存测试模式画布失败: {str(e)}")
            return False

    def close_canvas(self) -> bool:
        """关闭画布

        Returns:
            是否关闭成功
        """
        if not self.initialized:
            return True  # 未初始化，无需关闭

        try:
            # 释放资源
            self.canvas = None
            self.draw = None

            log.info("测试模式画布已关闭")
            return True
        except Exception as e:
            log.error(f"关闭测试模式画布失败: {str(e)}")
            return False

    def cleanup(self) -> bool:
        """清理资源

        Returns:
            是否清理成功
        """
        if not self.initialized:
            return True  # 未初始化，无需清理

        try:
            # 释放资源
            self.canvas = None
            self.draw = None

            log.info("测试模式资源已清理")
            return True
        except Exception as e:
            log.error(f"清理测试模式资源失败: {str(e)}")
            return False

    def generate_description(self, output_path: str, images_info: List[Dict[str, Any]],
                            canvas_info: Dict[str, Any]) -> bool:
        """生成测试模式说明文档

        Args:
            output_path: 输出路径
            images_info: 图片信息列表
            canvas_info: 画布信息

        Returns:
            是否生成成功
        """
        try:
            # 创建同名的TXT文件路径
            doc_path = os.path.splitext(output_path)[0] + "_说明.txt"
            log.info(f"生成测试模式说明文档: {doc_path}")

            # 确保输出目录存在
            output_dir = os.path.dirname(doc_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 提取画布信息
            canvas_name = canvas_info.get('canvas_name', os.path.basename(output_path))
            material_name = canvas_info.get('material_name', '')
            canvas_sequence = canvas_info.get('canvas_sequence', 0)
            canvas_width_m = canvas_info.get('canvas_width_m', 0)
            canvas_width_px = canvas_info.get('canvas_width_px', 0)
            canvas_height = canvas_info.get('canvas_height', 0)
            horizontal_expansion_cm = canvas_info.get('horizontal_expansion_cm', 0)
            max_height_cm = canvas_info.get('max_height_cm', 0)
            ppi = canvas_info.get('ppi', 72)

            # 计算画布高度（厘米）
            canvas_height_cm = canvas_height/ppi*2.54

            # 统计信息
            total_images = len(images_info)
            class_a_count = len([img for img in images_info if img.get('image_class') == 'A'])
            class_b_count = len([img for img in images_info if img.get('image_class') == 'B'])
            class_c_count = len([img for img in images_info if img.get('image_class') == 'C'])
            rotated_images = len([img for img in images_info if img.get('need_rotation', False) or img.get('rotated', False)])

            # 计算利用率
            if canvas_width_px > 0 and canvas_height > 0:
                total_area = canvas_width_px * canvas_height
                used_area = sum([img.get('width', 0) * img.get('height', 0) for img in images_info])
                utilization = used_area / total_area if total_area > 0 else 0
            else:
                utilization = 0

            # 获取当前时间
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 写入文件
            with open(doc_path, 'w', encoding='utf-8') as f:
                # 标题和基本信息
                f.write(f"{material_name}-{canvas_sequence} 测试模式报告\n")
                f.write(f"生成时间: {current_time}\n")
                f.write("★ 基本信息\n")
                f.write(f"材质名称: {material_name}\n")
                f.write(f"画布序号: {canvas_sequence}\n")
                f.write("算法类型: 图像排列算法\n")
                f.write("测试模式: 启用 (色块模拟)\n")

                # 容器详情
                f.write("★ 容器详情\n")
                f.write(f"基础宽度: {int(canvas_width_m*100)}cm\n")
                f.write(f"水平拓展: {horizontal_expansion_cm}cm\n")
                f.write(f"实际宽度: {int(canvas_width_m*100)}cm\n")
                f.write(f"最大高度: {max_height_cm}cm\n")
                f.write(f"实际高度: {canvas_height_cm:.0f}cm\n")
                f.write("单位转换: cm直接转px (测试模式)\n")
                f.write(f"测试全部数据: {'是' if self.is_test_all_data else '否'}\n")

                # 布局统计信息
                f.write("★ 布局统计\n")
                f.write(f"画布利用率: {utilization*100:.2f}%\n")
                f.write(f"旋转图片比例: {rotated_images/total_images*100:.2f}% ({rotated_images}/{total_images})\n")

                f.write(f"成功放置图片: {total_images}张\n")
                f.write(f"旋转图片数: {rotated_images}张\n")

                # 计算利用率评价
                if utilization >= 0.85:
                    rating = "★★★★★ 优秀"
                elif utilization >= 0.75:
                    rating = "★★★★☆ 良好"
                elif utilization >= 0.65:
                    rating = "★★★☆☆ 中等"
                elif utilization >= 0.50:
                    rating = "★★☆☆☆ 较差"
                else:
                    rating = "★☆☆☆☆ 待优化"

                f.write(f"利用率评价: {rating}\n")

                # 详细图片信息
                f.write("★ 详细图片信息\n")
                f.write("| 序号 | 图片标识 | 尺寸(px) | 位置(x,y) | 旋转 | 面积(px²) |\n")
                f.write("|------|----------|----------|-----------|------|-----------||\n")

                # 按Y坐标排序，使得输出更有序
                sorted_images = sorted(images_info, key=lambda img: (img.get('y', 0), img.get('x', 0)))

                for i, img in enumerate(sorted_images, 1):
                    name = img.get('name', f'Image_{i}')
                    width = img.get('width', 0)
                    height = img.get('height', 0)
                    x = img.get('x', 0)
                    y = img.get('y', 0)
                    rotated = '是' if (img.get('need_rotation', False) or img.get('rotated', False)) else '否'
                    area = width * height

                    f.write(f"| {i} | {name} | {width}x{height} | ({x},{y}) | {rotated} | {area:,} |\n")

                # 算法参数
                f.write("★ 算法参数\n")
                f.write("排序策略: 按面积排序\n")
                f.write("装箱算法: 智能排列算法\n")
                f.write("旋转功能: 启用\n")
                f.write("间距处理: 统一间距\n")
                f.write("优化目标: 最大化空间利用率\n")

            log.info(f"成功生成测试模式说明文档: {doc_path}")
            return True
        except Exception as e:
            log.error(f"生成测试模式说明文档失败: {str(e)}")
            return False


def get_image_processor(is_test_mode: bool, config: Dict[str, Any] = None) -> ImageProcessorBase:
    """获取图片处理器实例

    Args:
        is_test_mode: 是否测试模式
        config: 配置参数

    Returns:
        图片处理器实例
    """
    if config is None:
        config = {}

    if is_test_mode:
        processor = TestModeImageProcessor()
    else:
        processor = PhotoshopImageProcessor()

    # 初始化处理器
    processor.initialize(config)

    return processor