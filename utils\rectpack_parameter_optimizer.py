#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法参数优化器
专门用于优化RectPack算法参数，达到95%以上的利用率
"""

import logging
import time
from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass
import itertools
import copy

log = logging.getLogger(__name__)

@dataclass
class OptimizationResult:
    """优化结果"""
    utilization_rate: float
    parameters: Dict[str, Any]
    canvas_count: int
    total_area_used: float
    total_canvas_area: float
    processing_time: float
    success: bool
    details: str

class RectPackParameterOptimizer:
    """RectPack算法参数优化器"""

    def __init__(self, canvas_width_cm: float = 163.0, max_height_cm: float = 5000.0):
        """
        初始化优化器

        Args:
            canvas_width_cm: 画布宽度（cm）
            max_height_cm: 最大高度（cm）
        """
        self.canvas_width_cm = canvas_width_cm
        self.max_height_cm = max_height_cm

        log.info(f"RectPack参数优化器初始化: {canvas_width_cm}cm x {max_height_cm}cm")

    def optimize_parameters(self, image_data: List[Tuple[float, float, int]],
                          target_utilization: float = 95.0) -> OptimizationResult:
        """
        优化RectPack算法参数

        Args:
            image_data: 图片数据列表 [(宽度cm, 高度cm, 数量), ...]
            target_utilization: 目标利用率（%）

        Returns:
            OptimizationResult: 优化结果
        """
        log.info(f"开始优化RectPack参数，目标利用率: {target_utilization}%")
        start_time = time.time()

        # 准备图片数据
        images = self._prepare_image_data(image_data)
        log.info(f"准备了 {len(images)} 张图片进行优化测试")

        # 定义参数搜索空间
        parameter_space = self._define_parameter_space()

        best_result = None
        best_utilization = 0.0
        tested_combinations = 0

        # 遍历参数组合
        for params in parameter_space:
            tested_combinations += 1

            # 测试当前参数组合
            result = self._test_parameter_combination(images, params)

            if result.success and result.utilization_rate > best_utilization:
                best_utilization = result.utilization_rate
                best_result = result
                log.info(f"发现更好的参数组合: {result.utilization_rate:.2f}% "
                        f"(参数: {params})")

                # 如果达到目标利用率，可以提前结束
                if result.utilization_rate >= target_utilization:
                    log.info(f"达到目标利用率 {target_utilization}%，提前结束优化")
                    break

        processing_time = time.time() - start_time

        if best_result:
            best_result.processing_time = processing_time
            log.info(f"参数优化完成: 最佳利用率 {best_result.utilization_rate:.2f}%，"
                    f"测试了 {tested_combinations} 种参数组合，耗时 {processing_time:.2f}秒")
        else:
            log.warning(f"参数优化失败，测试了 {tested_combinations} 种参数组合")
            best_result = OptimizationResult(
                utilization_rate=0.0,
                parameters={},
                canvas_count=0,
                total_area_used=0.0,
                total_canvas_area=0.0,
                processing_time=processing_time,
                success=False,
                details="所有参数组合都失败"
            )

        return best_result

    def _prepare_image_data(self, image_data: List[Tuple[float, float, int]]) -> List[Dict[str, Any]]:
        """
        准备图片数据

        Args:
            image_data: 原始图片数据

        Returns:
            List[Dict]: 处理后的图片数据
        """
        images = []
        image_id = 1

        for width_cm, height_cm, count in image_data:
            # 创建指定数量的图片（直接使用cm单位）
            for _ in range(count):
                images.append({
                    'id': image_id,
                    'width': width_cm,
                    'height': height_cm,
                    'name': f'image_{image_id}',
                    'area': width_cm * height_cm
                })
                image_id += 1

        return images

    def _define_parameter_space(self) -> List[Dict[str, Any]]:
        """
        定义参数搜索空间

        Returns:
            List[Dict]: 参数组合列表
        """
        # 定义各个参数的候选值（简化版，减少组合数量）
        rotation_options = [True]  # 始终启用旋转

        # 排序策略：0=AREA, 1=PERIMETER, 3=SHORT_SIDE, 4=LONG_SIDE
        sort_strategies = [0, 1, 3]  # 重点测试面积、周长、短边排序

        # 装箱算法：0=BSSF, 1=BFF, 2=BBF
        pack_algorithms = [0, 1]  # 测试主要算法

        # 间距设置
        spacing_options = [1, 2]  # 1px和2px间距

        # 优化迭代次数
        iteration_options = [15, 20]  # 适中的迭代次数

        # 利用率阈值
        threshold_options = [92.0, 95.0]  # 高利用率目标

        # 旋转惩罚
        rotation_penalty_options = [0.001, 0.005]  # 极低惩罚

        # 宽高比偏好
        aspect_ratio_options = [1.0, 1.2]  # 不同宽高比偏好

        # 边长缩减容忍度
        shrink_tolerance_options = [0.5, 1.0]  # 不同缩减容忍度

        # 生成所有参数组合
        parameter_combinations = []

        for (rotation, sort_strategy, pack_algorithm, spacing, iterations,
             threshold, rotation_penalty, aspect_ratio, shrink_tolerance) in itertools.product(
            rotation_options, sort_strategies, pack_algorithms, spacing_options,
            iteration_options, threshold_options, rotation_penalty_options,
            aspect_ratio_options, shrink_tolerance_options
        ):
            parameter_combinations.append({
                'rotation_enabled': rotation,
                'sort_strategy': sort_strategy,
                'pack_algorithm': pack_algorithm,
                'spacing_px': spacing,
                'optimization_iterations': iterations,
                'min_utilization_threshold': threshold,
                'rotation_penalty': rotation_penalty,
                'aspect_ratio_preference': aspect_ratio,
                'edge_shrink_tolerance_cm': shrink_tolerance,
                'enable_optimization': True,
                'enable_edge_shrink': True,
                'enable_gap_filling': True,
                'enable_adaptive_sorting': True,
                'enable_multi_stage_optimization': True
            })

        log.info(f"生成了 {len(parameter_combinations)} 种参数组合进行测试")
        return parameter_combinations

    def _test_parameter_combination(self, images: List[Dict[str, Any]],
                                  params: Dict[str, Any]) -> OptimizationResult:
        """
        测试特定参数组合

        Args:
            images: 图片数据
            params: 参数组合

        Returns:
            OptimizationResult: 测试结果
        """
        try:
            # 导入RectPack相关模块
            from rectpack import newPacker, SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO
            from rectpack import MaxRectsBssf, MaxRectsBaf, MaxRectsBlsf

            # 排序算法映射
            sort_algos = [SORT_AREA, SORT_PERI, SORT_DIFF, SORT_SSIDE, SORT_LSIDE, SORT_RATIO]
            pack_algos = [MaxRectsBssf, MaxRectsBaf, MaxRectsBlsf]

            # 多容器装箱
            canvas_results = []
            remaining_images = copy.deepcopy(images)
            canvas_index = 0

            while remaining_images and canvas_index < 50:  # 最多50个画布
                # 为每个画布创建新的装箱器
                packer = newPacker(
                    pack_algo=pack_algos[params['pack_algorithm']],
                    sort_algo=sort_algos[params['sort_strategy']],
                    rotation=params['rotation_enabled']
                )

                # 添加容器（使用cm单位）
                packer.add_bin(self.canvas_width_cm, self.max_height_cm)

                # 添加当前批次的图片
                current_batch = []
                for img in remaining_images:
                    # 应用边长缩减（cm单位）
                    width = img['width']
                    height = img['height']

                    if params.get('enable_edge_shrink', False):
                        shrink_cm = params['edge_shrink_tolerance_cm']
                        width = max(0.1, width - shrink_cm)  # 最小0.1cm
                        height = max(0.1, height - shrink_cm)

                    # 添加间距（转换为cm）
                    spacing_cm = params['spacing_px'] * 2.54 / 72  # px转换为cm
                    width += spacing_cm
                    height += spacing_cm

                    packer.add_rect(width, height, rid=img['id'])
                    current_batch.append(img)

                # 执行装箱
                packer.pack()

                # 获取当前容器的结果
                placed_in_current = []
                max_y = 0
                total_area = 0

                for rect in packer.rect_list():
                    # RectPack返回的是元组: (bin_id, x, y, width, height, rid)
                    bin_id, _, y, width, height, rid = rect
                    if bin_id == 0:  # 第一个（也是唯一的）容器
                        placed_in_current.append(rid)
                        max_y = max(max_y, y + height)
                        total_area += width * height

                if placed_in_current:
                    canvas_area = self.canvas_width_cm * max_y
                    utilization = (total_area / canvas_area * 100) if canvas_area > 0 else 0

                    canvas_results.append({
                        'canvas_index': canvas_index,
                        'placed_count': len(placed_in_current),
                        'height': max_y,
                        'utilization': utilization,
                        'area_used': total_area,
                        'canvas_area': canvas_area
                    })

                    # 移除已放置的图片
                    remaining_images = [img for img in remaining_images if img['id'] not in placed_in_current]
                else:
                    # 如果没有图片被放置，停止
                    break

                canvas_index += 1

            # 计算总体利用率
            if canvas_results:
                total_area_used = sum(c['area_used'] for c in canvas_results)
                total_canvas_area = sum(c['canvas_area'] for c in canvas_results)
                overall_utilization = (total_area_used / total_canvas_area * 100) if total_canvas_area > 0 else 0

                return OptimizationResult(
                    utilization_rate=overall_utilization,
                    parameters=params,
                    canvas_count=len(canvas_results),
                    total_area_used=total_area_used,
                    total_canvas_area=total_canvas_area,
                    processing_time=0.0,
                    success=True,
                    details=f"成功放置到 {len(canvas_results)} 个画布，剩余 {len(remaining_images)} 张图片"
                )
            else:
                return OptimizationResult(
                    utilization_rate=0.0,
                    parameters=params,
                    canvas_count=0,
                    total_area_used=0.0,
                    total_canvas_area=0.0,
                    processing_time=0.0,
                    success=False,
                    details="无法放置任何图片"
                )

        except Exception as e:
            log.error(f"测试参数组合时出错: {str(e)}")
            return OptimizationResult(
                utilization_rate=0.0,
                parameters=params,
                canvas_count=0,
                total_area_used=0.0,
                total_canvas_area=0.0,
                processing_time=0.0,
                success=False,
                details=f"测试失败: {str(e)}"
            )

    def save_optimal_parameters_to_config(self, result: OptimizationResult) -> bool:
        """
        将最优参数保存到配置

        Args:
            result: 优化结果

        Returns:
            bool: 是否成功保存
        """
        if not result.success:
            log.error("无法保存失败的优化结果")
            return False

        try:
            from utils.config_manager_duckdb import ConfigManagerDuckDB
            config_manager = ConfigManagerDuckDB()

            # 构建要保存的配置
            config_updates = {}
            params = result.parameters

            # 基础参数
            config_updates['rectpack_rotation_enabled'] = params.get('rotation_enabled', True)
            config_updates['rectpack_sort_strategy'] = params.get('sort_strategy', 0)
            config_updates['rectpack_pack_algorithm'] = params.get('pack_algorithm', 0)
            config_updates['rectpack_spacing_px'] = params.get('spacing_px', 2)

            # 优化参数
            config_updates['rectpack_enable_optimization'] = params.get('enable_optimization', True)
            config_updates['rectpack_optimization_iterations'] = params.get('optimization_iterations', 15)
            config_updates['rectpack_min_utilization_threshold'] = params.get('min_utilization_threshold', 95.0)
            config_updates['rectpack_rotation_penalty'] = params.get('rotation_penalty', 0.005)
            config_updates['rectpack_aspect_ratio_preference'] = params.get('aspect_ratio_preference', 1.0)

            # 边长缩减参数
            config_updates['rectpack_enable_edge_shrink'] = params.get('enable_edge_shrink', True)
            config_updates['rectpack_edge_shrink_tolerance_cm'] = params.get('edge_shrink_tolerance_cm', 0.5)

            # 高级优化参数
            config_updates['rectpack_enable_gap_filling'] = params.get('enable_gap_filling', True)
            config_updates['rectpack_enable_adaptive_sorting'] = params.get('enable_adaptive_sorting', True)
            config_updates['rectpack_enable_multi_stage_optimization'] = params.get('enable_multi_stage_optimization', True)

            # 批量更新配置
            success = config_manager.update(config_updates)

            if success:
                log.info(f"成功保存最优RectPack参数到配置，利用率: {result.utilization_rate:.2f}%")
                return True
            else:
                log.error("保存最优参数到配置失败")
                return False

        except Exception as e:
            log.error(f"保存最优参数时出错: {str(e)}")
            return False

    def generate_optimization_report(self, result: OptimizationResult) -> str:
        """
        生成优化报告

        Args:
            result: 优化结果

        Returns:
            str: 优化报告
        """
        if not result.success:
            return f"RectPack参数优化失败: {result.details}"

        report = f"""
RectPack算法参数优化报告
========================

优化目标: 95%以上利用率
实际达到: {result.utilization_rate:.2f}%
画布数量: {result.canvas_count}
处理时间: {result.processing_time:.2f}秒

最优参数配置:
-----------
旋转启用: {result.parameters.get('rotation_enabled', 'N/A')}
排序策略: {result.parameters.get('sort_strategy', 'N/A')} (0=面积, 1=周长, 3=短边, 4=长边)
装箱算法: {result.parameters.get('pack_algorithm', 'N/A')} (0=BSSF, 1=BFF, 2=BBF)
图片间距: {result.parameters.get('spacing_px', 'N/A')}px
优化迭代: {result.parameters.get('optimization_iterations', 'N/A')}次
利用率阈值: {result.parameters.get('min_utilization_threshold', 'N/A')}%
旋转惩罚: {result.parameters.get('rotation_penalty', 'N/A')}
宽高比偏好: {result.parameters.get('aspect_ratio_preference', 'N/A')}
边长缩减容忍度: {result.parameters.get('edge_shrink_tolerance_cm', 'N/A')}cm

画布利用情况:
-----------
总使用面积: {result.total_area_used:.0f}px²
总画布面积: {result.total_canvas_area:.0f}px²
整体利用率: {result.utilization_rate:.2f}%

优化详情: {result.details}
"""
        return report


def main():
    """主函数 - 用于测试优化器"""
    # 测试数据
    IMAGE_DATA = [
        (190, 82, 1), (190, 82, 1), (190, 62, 1), (190, 82, 1), (190, 92, 1),
        (190, 82, 1), (190, 82, 1), (190, 92, 2), (190, 62, 1), (190, 82, 1),
        (190, 92, 1), (190, 92, 1), (190, 92, 1), (190, 92, 1), (185, 52, 1),
        (169.5, 34.5, 1), (163, 123, 1), (163, 123, 1), (155, 30, 1), (153, 62, 1),
        (153, 62, 1), (153, 22, 2), (153, 62, 1), (153, 62, 1), (143, 102, 1),
        (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1),
        (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1),
        (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1), (137, 51, 1),
        (137, 51, 1), (137, 51, 1), (123, 52, 1), (123, 102, 1), (123, 82, 2),
        (123, 32, 1), (123, 62, 1), (123, 62, 1), (123, 52, 1), (123, 62, 1),
        (123, 52, 1), (102, 52, 1), (102, 52, 1), (102, 52, 1), (102, 32, 1),
        (92, 52, 1), (92, 57, 1), (92, 42, 1), (92, 52, 1), (92, 52, 1),
        (82, 52, 1), (82, 32, 1), (82, 32, 1), (82, 52, 1), (77, 47, 1),
        (72, 47, 1), (72, 47, 1), (67, 52, 2), (67, 52, 1), (67, 52, 1),
        (67, 52, 2), (67, 52, 2), (67, 52, 1), (67, 52, 1), (67, 52, 2),
        (67, 52, 1), (67, 52, 2), (67, 52, 2), (67, 52, 2), (67, 52, 2),
        (67, 52, 2), (67, 52, 2), (67, 52, 2), (67, 52, 2), (67, 52, 2),
        (67, 52, 2), (67, 52, 2), (67, 52, 1), (67, 52, 1), (67, 52, 1),
        (67, 52, 1), (67, 52, 2), (67, 52, 1), (67, 52, 2), (67, 52, 2),
        (67, 52, 1), (67, 52, 1), (67, 52, 1), (67, 52, 2), (62, 42, 1),
        (62, 52, 1), (59, 35, 1), (59, 35, 1), (59, 35, 1), (59, 35, 1),
        (52, 52, 2), (52, 52, 2), (52, 52, 2), (245, 30, 1)
    ]

    # 创建优化器
    optimizer = RectPackParameterOptimizer(canvas_width_cm=163.0, max_height_cm=5000.0)

    # 执行优化
    result = optimizer.optimize_parameters(IMAGE_DATA, target_utilization=95.0)

    # 生成报告
    report = optimizer.generate_optimization_report(result)
    print(report)

    # 保存最优参数
    if result.success:
        optimizer.save_optimal_parameters_to_config(result)


if __name__ == "__main__":
    main()
