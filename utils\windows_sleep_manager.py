#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Windows休眠管理器
用于在任务运行时阻止系统休眠，任务结束时恢复休眠能力

功能说明：
1. 防止系统休眠：当任务开始时，阻止Windows系统进入休眠状态
2. 恢复休眠能力：当任务结束或应用退出时，恢复系统的休眠能力
3. 线程安全：支持多线程环境下的安全调用
4. 异常处理：提供完善的错误处理和日志记录

使用方法：
```python
from utils.windows_sleep_manager import WindowsSleepManager

# 创建管理器实例
sleep_manager = WindowsSleepManager()

# 开始任务时阻止休眠
sleep_manager.prevent_sleep("图像处理任务")

# 任务结束时恢复休眠
sleep_manager.allow_sleep()

# 或者使用上下文管理器
with sleep_manager.prevent_sleep_context("批量处理任务"):
    # 在这个代码块中，系统不会休眠
    do_some_work()
# 代码块结束后自动恢复休眠能力
```
"""

import os
import sys
import threading
import ctypes
from ctypes import wintypes
from contextlib import contextmanager
from typing import Optional
try:
    from utils.log_config import get_logger
except ImportError:
    # 如果无法导入，使用标准logging
    import logging
    def get_logger(name):
        return logging.getLogger(name)

log = get_logger("WindowsSleepManager")

# Windows API常量
ES_CONTINUOUS = 0x80000000
ES_SYSTEM_REQUIRED = 0x00000001
ES_DISPLAY_REQUIRED = 0x00000002
ES_AWAYMODE_REQUIRED = 0x00000040

class WindowsSleepManager:
    """
    Windows休眠管理器
    
    负责管理Windows系统的休眠状态，确保在重要任务执行期间
    系统不会进入休眠状态，任务完成后恢复正常的电源管理。
    """
    
    def __init__(self):
        """初始化休眠管理器"""
        self._lock = threading.Lock()
        self._is_sleep_prevented = False
        self._current_task_name = None
        self._original_execution_state = None
        
        # 检查是否在Windows系统上运行
        if not self._is_windows():
            log.warning("当前系统不是Windows，休眠管理功能将被禁用")
            self._windows_available = False
        else:
            self._windows_available = True
            log.info("Windows休眠管理器初始化成功")
    
    def _is_windows(self) -> bool:
        """检查是否在Windows系统上运行"""
        return sys.platform.startswith('win')
    
    def _set_thread_execution_state(self, es_flags: int) -> bool:
        """
        设置线程执行状态
        
        Args:
            es_flags: 执行状态标志
            
        Returns:
            bool: 是否设置成功
        """
        if not self._windows_available:
            return False
            
        try:
            # 获取kernel32.dll
            kernel32 = ctypes.windll.kernel32
            
            # 调用SetThreadExecutionState API
            result = kernel32.SetThreadExecutionState(es_flags)
            
            if result == 0:
                log.error("SetThreadExecutionState调用失败")
                return False
            
            return True
            
        except Exception as e:
            log.error(f"设置线程执行状态失败: {str(e)}")
            return False
    
    def prevent_sleep(self, task_name: str = "未知任务") -> bool:
        """
        阻止系统休眠
        
        Args:
            task_name: 任务名称，用于日志记录
            
        Returns:
            bool: 是否成功阻止休眠
        """
        with self._lock:
            if not self._windows_available:
                log.warning("Windows API不可用，无法阻止系统休眠")
                return False
            
            if self._is_sleep_prevented:
                log.info(f"系统休眠已被阻止（当前任务：{self._current_task_name}），新任务：{task_name}")
                self._current_task_name = task_name  # 更新任务名称
                return True
            
            # 设置执行状态：保持系统和显示器活跃
            es_flags = ES_CONTINUOUS | ES_SYSTEM_REQUIRED | ES_DISPLAY_REQUIRED
            
            if self._set_thread_execution_state(es_flags):
                self._is_sleep_prevented = True
                self._current_task_name = task_name
                log.info(f"✅ 已阻止系统休眠，当前任务：{task_name}")
                return True
            else:
                log.error(f"❌ 阻止系统休眠失败，任务：{task_name}")
                return False
    
    def allow_sleep(self) -> bool:
        """
        恢复系统休眠能力
        
        Returns:
            bool: 是否成功恢复休眠能力
        """
        with self._lock:
            if not self._windows_available:
                log.warning("Windows API不可用，无法恢复系统休眠")
                return False
            
            if not self._is_sleep_prevented:
                log.info("系统休眠未被阻止，无需恢复")
                return True
            
            # 恢复默认执行状态
            if self._set_thread_execution_state(ES_CONTINUOUS):
                task_name = self._current_task_name or "未知任务"
                self._is_sleep_prevented = False
                self._current_task_name = None
                log.info(f"✅ 已恢复系统休眠能力，完成任务：{task_name}")
                return True
            else:
                log.error("❌ 恢复系统休眠能力失败")
                return False
    
    def is_sleep_prevented(self) -> bool:
        """
        检查是否已阻止系统休眠
        
        Returns:
            bool: 是否已阻止休眠
        """
        with self._lock:
            return self._is_sleep_prevented
    
    def get_current_task(self) -> Optional[str]:
        """
        获取当前任务名称
        
        Returns:
            Optional[str]: 当前任务名称，如果没有任务则返回None
        """
        with self._lock:
            return self._current_task_name
    
    @contextmanager
    def prevent_sleep_context(self, task_name: str = "上下文任务"):
        """
        上下文管理器，自动管理休眠状态
        
        Args:
            task_name: 任务名称
            
        Usage:
            with sleep_manager.prevent_sleep_context("重要任务"):
                # 在这个代码块中系统不会休眠
                do_important_work()
            # 代码块结束后自动恢复休眠能力
        """
        # 进入上下文时阻止休眠
        success = self.prevent_sleep(task_name)
        
        try:
            yield success
        finally:
            # 退出上下文时恢复休眠
            if success:
                self.allow_sleep()
    
    def force_reset(self) -> bool:
        """
        强制重置休眠状态
        
        在异常情况下使用，强制恢复系统休眠能力
        
        Returns:
            bool: 是否重置成功
        """
        with self._lock:
            if not self._windows_available:
                log.warning("Windows API不可用，无法重置休眠状态")
                return False
            
            log.warning("执行强制重置休眠状态")
            
            # 强制恢复默认执行状态
            if self._set_thread_execution_state(ES_CONTINUOUS):
                self._is_sleep_prevented = False
                self._current_task_name = None
                log.info("✅ 强制重置休眠状态成功")
                return True
            else:
                log.error("❌ 强制重置休眠状态失败")
                return False
    
    def get_status_info(self) -> dict:
        """
        获取休眠管理器状态信息
        
        Returns:
            dict: 状态信息字典
        """
        with self._lock:
            return {
                'windows_available': self._windows_available,
                'is_sleep_prevented': self._is_sleep_prevented,
                'current_task': self._current_task_name,
                'platform': sys.platform
            }
    
    def __del__(self):
        """析构函数，确保在对象销毁时恢复休眠能力"""
        try:
            if hasattr(self, '_is_sleep_prevented') and self._is_sleep_prevented:
                log.warning("休眠管理器被销毁时检测到休眠仍被阻止，正在恢复...")
                self.allow_sleep()
        except Exception as e:
            log.error(f"析构函数中恢复休眠失败: {str(e)}")


# 全局单例实例
_global_sleep_manager = None
_global_lock = threading.Lock()

def get_global_sleep_manager() -> WindowsSleepManager:
    """
    获取全局休眠管理器单例
    
    Returns:
        WindowsSleepManager: 全局休眠管理器实例
    """
    global _global_sleep_manager
    
    if _global_sleep_manager is None:
        with _global_lock:
            if _global_sleep_manager is None:
                _global_sleep_manager = WindowsSleepManager()
    
    return _global_sleep_manager

def prevent_system_sleep(task_name: str = "全局任务") -> bool:
    """
    阻止系统休眠的便捷函数
    
    Args:
        task_name: 任务名称
        
    Returns:
        bool: 是否成功阻止休眠
    """
    return get_global_sleep_manager().prevent_sleep(task_name)

def allow_system_sleep() -> bool:
    """
    恢复系统休眠能力的便捷函数
    
    Returns:
        bool: 是否成功恢复休眠能力
    """
    return get_global_sleep_manager().allow_sleep()

def is_system_sleep_prevented() -> bool:
    """
    检查系统休眠是否被阻止的便捷函数
    
    Returns:
        bool: 是否已阻止休眠
    """
    return get_global_sleep_manager().is_sleep_prevented()

def get_current_sleep_task() -> Optional[str]:
    """
    获取当前阻止休眠的任务名称
    
    Returns:
        Optional[str]: 当前任务名称
    """
    return get_global_sleep_manager().get_current_task()

# 应用退出时的清理函数
def cleanup_sleep_manager():
    """
    清理休眠管理器，恢复系统休眠能力
    
    应在应用程序退出时调用
    """
    global _global_sleep_manager
    
    if _global_sleep_manager is not None:
        try:
            if _global_sleep_manager.is_sleep_prevented():
                log.info("应用退出时恢复系统休眠能力")
                _global_sleep_manager.allow_sleep()
        except Exception as e:
            log.error(f"清理休眠管理器时出错: {str(e)}")
        finally:
            _global_sleep_manager = None

if __name__ == "__main__":
    # 测试代码
    import time
    
    print("测试Windows休眠管理器...")
    
    # 创建管理器
    manager = WindowsSleepManager()
    
    # 显示状态
    print(f"初始状态: {manager.get_status_info()}")
    
    # 阻止休眠
    print("\n阻止系统休眠...")
    success = manager.prevent_sleep("测试任务")
    print(f"阻止休眠结果: {success}")
    print(f"当前状态: {manager.get_status_info()}")
    
    # 等待几秒
    print("\n等待5秒...")
    time.sleep(5)
    
    # 恢复休眠
    print("\n恢复系统休眠...")
    success = manager.allow_sleep()
    print(f"恢复休眠结果: {success}")
    print(f"最终状态: {manager.get_status_info()}")
    
    # 测试上下文管理器
    print("\n测试上下文管理器...")
    with manager.prevent_sleep_context("上下文测试"):
        print(f"上下文中状态: {manager.get_status_info()}")
        time.sleep(2)
    print(f"上下文后状态: {manager.get_status_info()}")
    
    print("\n测试完成！")