#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack约束优化测试脚本
测试新增的约束条件和优化策略：
1. 宽度间隙限制：画布宽度 - 已放置图片总宽度 ≤ 10cm
2. 边长微调：每张图片边长可缩减最多1cm
3. 智能旋转和分组优化
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.rectpack_constraint_manager import RectPackConstraintManager, ImageAdjustment
from core.unified_image_arranger import UnifiedImageArranger

def test_constraint_manager():
    """测试约束管理器的基本功能"""
    print("=" * 60)
    print("测试约束管理器基本功能")
    print("=" * 60)
    
    # 创建约束管理器：163cm画布，最大间隙10cm，最大缩减1cm
    manager = RectPackConstraintManager(
        canvas_width_cm=163.0,
        max_width_gap_cm=10.0,
        max_edge_reduction_cm=1.0
    )
    
    print(f"约束管理器配置:")
    print(f"  - 画布宽度: {manager.canvas_width_cm}cm")
    print(f"  - 最大宽度间隙: {manager.max_width_gap_cm}cm")
    print(f"  - 最大边长缩减: {manager.max_edge_reduction_cm}cm")
    
    # 测试宽度间隙检查
    print(f"\n测试宽度间隙检查:")
    
    # 模拟已放置的图片（像素单位，72 PPI）
    placed_images = [
        {'width': 3883, 'height': 1446},  # 137x51cm
        {'width': 3883, 'height': 1446},  # 137x51cm
        {'width': 3883, 'height': 1446},  # 137x51cm
    ]
    
    violation = manager.check_width_gap_constraint(placed_images)
    if violation:
        print(f"  ❌ 发现约束违反: {violation}")
    else:
        print(f"  ✅ 宽度间隙检查通过")
    
    # 测试边长微调策略
    print(f"\n测试边长微调策略:")
    
    image_data = {
        'unique_id': 'test_image_1',
        'name': '测试图片1',
        'width_cm': 137.0,
        'height_cm': 51.0
    }
    
    adjusted_width, adjusted_height, was_adjusted = manager.apply_edge_reduction_strategy(
        137.0, 51.0, image_data
    )
    
    if was_adjusted:
        print(f"  ✅ 边长微调成功:")
        print(f"    原始尺寸: 137.0x51.0cm")
        print(f"    调整后尺寸: {adjusted_width:.1f}x{adjusted_height:.1f}cm")
        print(f"    缩减量: {137.0 - adjusted_width:.1f}cm x {51.0 - adjusted_height:.1f}cm")
    else:
        print(f"  ➡️ 无需边长微调")
    
    # 获取统计信息
    stats = manager.get_constraint_statistics()
    print(f"\n约束管理器统计:")
    for key, value in stats.items():
        print(f"  - {key}: {value}")

def test_width_gap_scenarios():
    """测试不同宽度间隙场景"""
    print("\n" + "=" * 60)
    print("测试不同宽度间隙场景")
    print("=" * 60)
    
    # 测试场景：163cm画布
    canvas_width_cm = 163.0
    manager = RectPackConstraintManager(canvas_width_cm=canvas_width_cm)
    
    test_scenarios = [
        {
            'name': '3张137x51cm图片（原始方向）',
            'images': [{'width': 3883, 'height': 1446}] * 3,  # 137x51cm
            'expected_gap': 163.0 - 137.0 * 3  # 163 - 411 = -248cm (超出)
        },
        {
            'name': '3张51x137cm图片（旋转后）',
            'images': [{'width': 1446, 'height': 3883}] * 3,  # 51x137cm
            'expected_gap': 163.0 - 51.0 * 3  # 163 - 153 = 10cm (刚好)
        },
        {
            'name': '2张137x51cm图片',
            'images': [{'width': 3883, 'height': 1446}] * 2,  # 137x51cm
            'expected_gap': 163.0 - 137.0 * 2  # 163 - 274 = -111cm (超出)
        },
        {
            'name': '4张40x30cm图片',
            'images': [{'width': 1134, 'height': 850}] * 4,  # 40x30cm
            'expected_gap': 163.0 - 40.0 * 4  # 163 - 160 = 3cm (符合)
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n场景: {scenario['name']}")
        violation = manager.check_width_gap_constraint(scenario['images'])
        
        if violation:
            print(f"  ❌ 约束违反: {violation.message}")
            print(f"  预期间隙: {scenario['expected_gap']:.1f}cm")
        else:
            print(f"  ✅ 约束满足")
            print(f"  预期间隙: {scenario['expected_gap']:.1f}cm ≤ 10cm")

def test_edge_reduction_optimization():
    """测试边长微调优化"""
    print("\n" + "=" * 60)
    print("测试边长微调优化")
    print("=" * 60)
    
    manager = RectPackConstraintManager(
        canvas_width_cm=163.0,
        max_width_gap_cm=10.0,
        max_edge_reduction_cm=1.0
    )
    
    # 测试场景：需要缩减总宽度的图片组合
    images = [
        {'width_cm': 55.0, 'height_cm': 40.0, 'unique_id': 'img1', 'name': '图片1'},
        {'width_cm': 55.0, 'height_cm': 40.0, 'unique_id': 'img2', 'name': '图片2'},
        {'width_cm': 55.0, 'height_cm': 40.0, 'unique_id': 'img3', 'name': '图片3'},
    ]
    
    # 当前总宽度：55 * 3 = 165cm，超出163cm画布2cm
    target_total_width = 163.0 - 10.0  # 目标总宽度：153cm
    
    print(f"优化场景:")
    print(f"  - 画布宽度: 163cm")
    print(f"  - 当前总宽度: {sum(img['width_cm'] for img in images)}cm")
    print(f"  - 目标总宽度: {target_total_width}cm")
    print(f"  - 需要缩减: {sum(img['width_cm'] for img in images) - target_total_width}cm")
    
    adjustments = manager.calculate_optimal_adjustments(images, target_total_width)
    
    print(f"\n优化结果:")
    total_reduction = 0
    for adjustment in adjustments:
        print(f"  - {adjustment}")
        total_reduction += adjustment.get_total_reduction()
    
    print(f"\n总缩减量: {total_reduction:.1f}cm")
    
    # 验证优化后的总宽度
    optimized_total_width = sum(adj.adjusted_width for adj in adjustments)
    print(f"优化后总宽度: {optimized_total_width:.1f}cm")
    print(f"是否满足目标: {'✅' if optimized_total_width <= target_total_width else '❌'}")

def test_integrated_optimization():
    """测试集成优化：智能分组 + 约束管理"""
    print("\n" + "=" * 60)
    print("测试集成优化：智能分组 + 约束管理")
    print("=" * 60)
    
    # 创建测试数据：模拟用户的实际场景
    pattern_items = []
    
    # 添加大量137x51cm的图片
    for i in range(20):
        pattern_items.append({
            'pattern_name': f'主要图片_{i+1}',
            'width_cm': 137.0,
            'height_cm': 51.0,
            'path': f'test_main_{i+1}.jpg',
            'quantity': 1
        })
    
    # 添加一些其他尺寸的图片
    other_sizes = [
        (120.0, 45.0, 5),   # 5张120x45cm
        (100.0, 60.0, 3),   # 3张100x60cm
        (80.0, 40.0, 8),    # 8张80x40cm
    ]
    
    for width, height, count in other_sizes:
        for i in range(count):
            pattern_items.append({
                'pattern_name': f'其他图片_{width}x{height}_{i+1}',
                'width_cm': width,
                'height_cm': height,
                'path': f'test_other_{width}x{height}_{i+1}.jpg',
                'quantity': 1
            })
    
    print(f"测试数据: {len(pattern_items)} 张图片")
    
    # 创建统一图片排列器（包含智能分组优化器和约束管理器）
    try:
        arranger = UnifiedImageArranger()
        
        # 初始化：163cm画布，72 PPI
        canvas_width_px = int(163.0 * 72 / 2.54)  # 转换为像素
        max_height_px = int(200.0 * 72 / 2.54)    # 最大高度200cm
        
        success = arranger.initialize(
            canvas_width_px=canvas_width_px,
            max_height_px=max_height_px,
            image_spacing_px=0,
            ppi=72,
            is_test_mode=True
        )
        
        if not success:
            print("❌ 统一图片排列器初始化失败")
            return
            
        print(f"✅ 统一图片排列器初始化成功")
        print(f"  - 画布尺寸: {canvas_width_px}x{max_height_px}px")
        print(f"  - 画布尺寸: 163.0x200.0cm")
        
        # 执行排列
        print(f"\n开始执行集成优化排列...")
        arranged_images = arranger.arrange_images(pattern_items)
        
        print(f"✅ 排列完成: {len(arranged_images)} 张图片")
        
        # 获取布局统计
        stats = arranger.get_layout_statistics()
        if stats:
            print(f"\n布局统计:")
            print(f"  - 利用率: {stats.get('utilization_percent', 0):.2f}%")
            print(f"  - 画布尺寸: {stats.get('canvas_width_px', 0)}x{stats.get('canvas_height_px', 0)}px")
            print(f"  - 已放置图片: {stats.get('placed_count', 0)}")
        
        # 获取约束统计（如果可用）
        if hasattr(arranger.rectpack_arranger, 'get_constraint_statistics'):
            constraint_stats = arranger.rectpack_arranger.get_constraint_statistics()
            print(f"\n约束统计:")
            for key, value in constraint_stats.items():
                print(f"  - {key}: {value}")
        
        # 分析优化效果
        print(f"\n优化效果分析:")
        rotated_count = sum(1 for img in arranged_images if img.get('rotated', False))
        adjusted_count = sum(1 for img in arranged_images if img.get('edge_adjusted', False))
        
        print(f"  - 旋转图片数量: {rotated_count}/{len(arranged_images)}")
        print(f"  - 边长调整图片数量: {adjusted_count}/{len(arranged_images)}")
        
        # 检查主要尺寸图片的优化情况
        main_size_images = [img for img in arranged_images 
                           if abs(img.get('width_cm', 0) - 137.0) < 0.1 and abs(img.get('height_cm', 0) - 51.0) < 0.1]
        main_size_rotated = sum(1 for img in main_size_images if img.get('rotated', False))
        
        print(f"  - 137x51cm图片优化: {main_size_rotated}/{len(main_size_images)} 旋转")
        
        if main_size_rotated > 0:
            print(f"  ✅ 智能分组优化生效：137x51cm图片旋转为51x137cm")
            
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("RectPack约束优化测试")
    print("测试新增的约束条件和优化策略")
    
    # 测试1：约束管理器基本功能
    test_constraint_manager()
    
    # 测试2：不同宽度间隙场景
    test_width_gap_scenarios()
    
    # 测试3：边长微调优化
    test_edge_reduction_optimization()
    
    # 测试4：集成优化测试
    test_integrated_optimization()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
