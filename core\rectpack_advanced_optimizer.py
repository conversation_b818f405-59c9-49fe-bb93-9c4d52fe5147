#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RectPack高级优化器
实现90%+利用率的优化算法

主要功能：
1. 自适应排序策略
2. 间隙填充算法
3. 智能旋转决策
4. 多阶段优化
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from PyQt5.QtCore import QObject, pyqtSignal
import math

log = logging.getLogger(__name__)


class RectPackAdvancedOptimizer:
    """RectPack高级优化器"""
    
    def __init__(self, arranger):
        """
        初始化优化器
        
        Args:
            arranger: RectPackArranger实例
        """
        self.arranger = arranger
        self.log_signal = getattr(arranger, 'log_signal', None)
    
    def adaptive_sorting_optimization(self, original_state: Dict[str, Any]) -> Tuple[Optional[Tuple], float]:
        """
        自适应排序优化：根据图片特征选择最优排序策略
        
        Args:
            original_state: 原始状态
            
        Returns:
            Tuple[Optional[Tuple], float]: (最佳配置, 最佳利用率)
        """
        images = original_state['images']
        if not images:
            return None, 0.0
        
        # 分析图片特征
        total_area = sum(img.get('width', 0) * img.get('height', 0) for img in images)
        avg_area = total_area / len(images) if images else 0
        
        # 计算宽高比分布
        aspect_ratios = []
        for img in images:
            width = img.get('width', 1)
            height = img.get('height', 1)
            aspect_ratios.append(width / height if height > 0 else 1.0)
        
        avg_aspect_ratio = sum(aspect_ratios) / len(aspect_ratios) if aspect_ratios else 1.0
        
        # 根据特征选择排序策略
        if avg_aspect_ratio > 1.5:  # 偏横向图片
            optimal_strategies = ['height_desc', 'area_desc', 'width_desc']
        elif avg_aspect_ratio < 0.7:  # 偏纵向图片
            optimal_strategies = ['width_desc', 'area_desc', 'height_desc']
        else:  # 接近正方形
            optimal_strategies = ['area_desc', 'perimeter_desc', 'max_side_desc']
        
        if self.log_signal:
            self.log_signal.emit(f"🔍 图片特征分析: 平均宽高比={avg_aspect_ratio:.2f}, 选择策略={optimal_strategies}")
        
        best_config = None
        best_utilization = 0.0
        
        # 测试每种策略
        for strategy in optimal_strategies:
            for rotation in [True, False]:
                for pack_algo in ['BSSF', 'BLSF', 'BAF']:
                    config = (rotation, strategy, pack_algo)
                    utilization = self.arranger._test_configuration(config, images)
                    
                    if utilization > best_utilization:
                        best_utilization = utilization
                        best_config = config
                        
                        if self.log_signal:
                            self.log_signal.emit(f"✅ 发现更优配置: {config}, 利用率={utilization:.2f}%")
        
        return best_config, best_utilization
    
    def gap_filling_optimization(self) -> float:
        """
        间隙填充优化：包含传统间隙填充、边长缩减填充和小图片填充优化
        
        Returns:
            float: 优化后的利用率
        """
        if not self.arranger.placed_images:
            return 0.0
        
        # 获取当前布局信息
        current_stats = self.arranger.get_layout_info()
        original_utilization = current_stats['utilization_percent']
        
        if self.log_signal:
            self.log_signal.emit(f"🔧 开始综合间隙填充优化，当前利用率: {original_utilization:.2f}%")
        
        # 第一阶段：传统间隙填充
        traditional_utilization = self._traditional_gap_filling()
        
        # 第二阶段：边长缩减优化（如果启用）
        edge_shrink_utilization = traditional_utilization
        if getattr(self.arranger, 'enable_edge_shrink', True):
            edge_shrink_utilization = self._edge_shrink_gap_filling()
        
        # 第三阶段：小图片填充大图片缝隙
        final_utilization = edge_shrink_utilization
        if getattr(self.arranger, 'small_image_fill_enabled', True):
            final_utilization = self._small_image_fill_optimization()
        
        final_improvement = final_utilization - original_utilization
        
        if self.log_signal:
            self.log_signal.emit(f"✅ 综合间隙填充优化完成")
            self.log_signal.emit(f"📊 利用率提升: {original_utilization:.2f}% -> {final_utilization:.2f}% (+{final_improvement:.2f}%)")
        
        return final_utilization
    
    def _traditional_gap_filling(self) -> float:
        """
        传统间隙填充：重新定位小图片到更优位置
        
        Returns:
            float: 优化后的利用率
        """
        # 识别小图片（相对于平均面积）
        total_area = sum(img['width'] * img['height'] for img in self.arranger.placed_images)
        avg_area = total_area / len(self.arranger.placed_images) if self.arranger.placed_images else 0
        small_threshold = avg_area * getattr(self.arranger, 'small_item_threshold', 0.05)
        
        small_images = [img for img in self.arranger.placed_images 
                       if img['width'] * img['height'] <= small_threshold]
        
        if not small_images:
            if self.log_signal:
                self.log_signal.emit("⚠️ 未发现可优化的小图片")
            current_stats = self.arranger.get_layout_info()
            return current_stats['utilization_percent']
        
        if self.log_signal:
            self.log_signal.emit(f"🎯 发现 {len(small_images)} 个小图片可进行位置优化")
        
        # 寻找空隙并尝试重新放置小图片
        gaps_filled = 0
        for small_img in small_images:
            # 尝试找到更好的位置
            better_position = self._find_better_position(small_img)
            if better_position:
                # 移动图片到更好的位置
                old_x, old_y = small_img['x'], small_img['y']
                small_img['x'] = better_position[0]
                small_img['y'] = better_position[1]
                gaps_filled += 1
                
                if self.log_signal:
                    self.log_signal.emit(f"📍 图片移动: ({old_x},{old_y}) -> ({better_position[0]},{better_position[1]})")
        
        # 计算优化后的利用率
        if gaps_filled > 0:
            new_stats = self.arranger.get_layout_info()
            new_utilization = new_stats['utilization_percent']
            
            if self.log_signal:
                self.log_signal.emit(f"✅ 传统间隙填充完成: 移动了 {gaps_filled} 个图片")
            
            return new_utilization
        
        current_stats = self.arranger.get_layout_info()
        return current_stats['utilization_percent']
    
    def _find_better_position(self, image: Dict[str, Any]) -> Optional[Tuple[int, int]]:
        """
        为图片寻找更好的位置以提高利用率
        
        Args:
            image: 图片信息
            
        Returns:
            Optional[Tuple[int, int]]: 更好的位置坐标，如果没有则返回None
        """
        width = image['width']
        height = image['height']
        current_x = image['x']
        current_y = image['y']
        
        # 扫描可能的位置
        best_position = None
        best_score = self._calculate_position_score(current_x, current_y, width, height)
        
        # 获取容器尺寸
        container_width = getattr(self.arranger, 'container_width', 1000)
        max_height = self.arranger.get_max_height() if hasattr(self.arranger, 'get_max_height') else 1000
        
        # 在较小的搜索范围内寻找更好的位置
        step_size = max(5, min(width, height) // 8)
        
        for x in range(0, container_width - width + 1, step_size):
            for y in range(0, max_height - height + 1, step_size):
                if x == current_x and y == current_y:
                    continue
                
                # 检查是否与其他图片重叠
                if not self._check_overlap(x, y, width, height, exclude_image=image):
                    # 计算位置得分（越靠近左上角越好，越紧凑越好）
                    score = self._calculate_position_score(x, y, width, height)
                    if score > best_score:
                        best_score = score
                        best_position = (x, y)
        
        return best_position
    
    def _edge_shrink_gap_filling(self) -> float:
        """
        边长缩减间隙填充：允许图片边长减小指定容忍度来填充更多缝隙
        
        Returns:
            float: 优化后的利用率
        """
        if self.log_signal:
            self.log_signal.emit("🔧 开始边长缩减间隙填充优化")
        
        # 获取边长缩减容忍度（cm转换为像素）
        tolerance_cm = getattr(self.arranger, 'edge_shrink_tolerance_cm', 0.5)
        ppi = getattr(self.arranger, 'ppi', 72)
        tolerance_px = int(tolerance_cm * ppi / 2.54)  # cm转像素
        
        if self.log_signal:
            self.log_signal.emit(f"📏 边长缩减容忍度: {tolerance_cm}cm ({tolerance_px}px)")
        
        # 按面积从小到大排序，优先处理小图片
        sorted_images = sorted(self.arranger.placed_images, 
                             key=lambda img: img['width'] * img['height'])
        
        optimized_count = 0
        total_shrink_area = 0
        
        for img in sorted_images:
            original_width = img['width']
            original_height = img['height']
            original_area = original_width * original_height
            
            # 尝试不同的缩减组合，优先考虑水平方向缩减
            best_shrink = None
            best_position = None
            best_area_saved = 0
            
            # 优先尝试水平方向缩减（width_shrink优先）
            for width_shrink in range(0, min(tolerance_px + 1, original_width // 4)):
                for height_shrink in range(0, min(tolerance_px + 1, original_height // 4)):
                    if width_shrink == 0 and height_shrink == 0:
                        continue
                    
                    new_width = original_width - width_shrink
                    new_height = original_height - height_shrink
                    
                    if new_width <= 0 or new_height <= 0:
                        continue
                    
                    # 寻找更好的位置
                    better_pos = self._find_position_for_shrunken_image(
                        img, new_width, new_height)
                    
                    if better_pos:
                        # 计算节省的面积，水平缩减给予更高权重
                        horizontal_weight = 1.5  # 水平缩减权重更高
                        area_saved = (width_shrink * horizontal_weight * original_height + 
                                     height_shrink * new_width)
                        
                        if area_saved > best_area_saved:
                            best_area_saved = area_saved
                            best_shrink = (width_shrink, height_shrink)
                            best_position = better_pos
            
            # 应用最佳缩减
            if best_shrink and best_position:
                width_shrink, height_shrink = best_shrink
                img['width'] = original_width - width_shrink
                img['height'] = original_height - height_shrink
                img['x'] = best_position[0]
                img['y'] = best_position[1]
                img['edge_shrunk'] = True  # 标记为已缩减
                img['shrink_amount_cm'] = (width_shrink + height_shrink) * 2.54 / ppi
                
                optimized_count += 1
                total_shrink_area += best_area_saved
                
                if self.log_signal:
                    self.log_signal.emit(f"📐 图片缩减: {original_width}x{original_height} -> {img['width']}x{img['height']}")
        
        if optimized_count > 0:
            current_stats = self.arranger.get_layout_info()
            if self.log_signal:
                self.log_signal.emit(f"✅ 边长缩减优化完成: {optimized_count}个图片被优化，节省{total_shrink_area}px²空间")
            return current_stats['utilization_percent']
        
        if self.log_signal:
            self.log_signal.emit("ℹ️ 未找到可缩减优化的图片")
        
        current_stats = self.arranger.get_layout_info()
        return current_stats['utilization_percent']
    
    def _small_image_fill_optimization(self) -> float:
        """
        小图片填充大图片缝隙优化：用小块面积的图片填充大块图片的缝隙和边缘
        
        Returns:
            float: 优化后的利用率
        """
        if self.log_signal:
            self.log_signal.emit("🔧 开始小图片填充大图片缝隙优化")
        
        # 按面积分类图片
        total_area = sum(img['width'] * img['height'] for img in self.arranger.placed_images)
        avg_area = total_area / len(self.arranger.placed_images) if self.arranger.placed_images else 0
        
        # 小图片阈值：平均面积的20%
        small_threshold = avg_area * 0.2
        # 大图片阈值：平均面积的150%
        large_threshold = avg_area * 1.5
        
        small_images = [img for img in self.arranger.placed_images 
                       if img['width'] * img['height'] <= small_threshold]
        large_images = [img for img in self.arranger.placed_images 
                       if img['width'] * img['height'] >= large_threshold]
        
        if not small_images or not large_images:
            if self.log_signal:
                self.log_signal.emit("⚠️ 未发现合适的小图片或大图片进行填充优化")
            current_stats = self.arranger.get_layout_info()
            return current_stats['utilization_percent']
        
        if self.log_signal:
            self.log_signal.emit(f"🎯 发现 {len(small_images)} 个小图片和 {len(large_images)} 个大图片")
        
        filled_count = 0
        
        # 为每个小图片寻找大图片附近的最佳位置
        for small_img in small_images:
            best_position = self._find_position_near_large_images(
                small_img, large_images)
            
            if best_position:
                old_x, old_y = small_img['x'], small_img['y']
                small_img['x'] = best_position[0]
                small_img['y'] = best_position[1]
                small_img['gap_filled'] = True  # 标记为缝隙填充
                filled_count += 1
                
                if self.log_signal:
                    self.log_signal.emit(f"🧩 小图片填充: ({old_x},{old_y}) -> ({best_position[0]},{best_position[1]})")
        
        if filled_count > 0:
            current_stats = self.arranger.get_layout_info()
            if self.log_signal:
                self.log_signal.emit(f"✅ 小图片填充优化完成: {filled_count}个小图片重新定位")
            return current_stats['utilization_percent']
        
        if self.log_signal:
            self.log_signal.emit("ℹ️ 未找到合适的填充位置")
        
        current_stats = self.arranger.get_layout_info()
        return current_stats['utilization_percent']
    
    def _find_position_for_shrunken_image(self, image: Dict[str, Any], 
                                         new_width: int, new_height: int) -> Optional[Tuple[int, int]]:
        """
        为缩减后的图片寻找更好的位置
        
        Args:
            image: 原图片信息
            new_width: 缩减后的宽度
            new_height: 缩减后的高度
            
        Returns:
            Optional[Tuple[int, int]]: 更好的位置坐标
        """
        container_width = getattr(self.arranger, 'container_width', 1000)
        max_height = self.arranger.get_max_height() if hasattr(self.arranger, 'get_max_height') else 1000
        
        # 搜索步长
        step_size = max(5, min(new_width, new_height) // 10)
        
        best_position = None
        best_score = -1
        
        for x in range(0, container_width - new_width + 1, step_size):
            for y in range(0, max_height - new_height + 1, step_size):
                # 检查是否与其他图片重叠
                if not self._check_overlap(x, y, new_width, new_height, exclude_image=image):
                    # 计算位置得分（优先靠近左上角和其他图片）
                    score = self._calculate_position_score(x, y, new_width, new_height)
                    if score > best_score:
                        best_score = score
                        best_position = (x, y)
        
        return best_position
    
    def _find_position_near_large_images(self, small_image: Dict[str, Any], 
                                        large_images: List[Dict[str, Any]]) -> Optional[Tuple[int, int]]:
        """
        为小图片寻找大图片附近的最佳位置
        
        Args:
            small_image: 小图片信息
            large_images: 大图片列表
            
        Returns:
            Optional[Tuple[int, int]]: 最佳位置坐标
        """
        width = small_image['width']
        height = small_image['height']
        
        best_position = None
        best_score = -1
        
        # 在每个大图片周围搜索位置
        for large_img in large_images:
            large_x = large_img['x']
            large_y = large_img['y']
            large_w = large_img['width']
            large_h = large_img['height']
            
            # 搜索大图片周围的位置
            search_positions = [
                # 右侧
                (large_x + large_w, large_y),
                (large_x + large_w, large_y + large_h - height),
                # 下方
                (large_x, large_y + large_h),
                (large_x + large_w - width, large_y + large_h),
                # 左侧（如果有空间）
                (max(0, large_x - width), large_y),
                (max(0, large_x - width), large_y + large_h - height),
                # 上方（如果有空间）
                (large_x, max(0, large_y - height)),
                (large_x + large_w - width, max(0, large_y - height))
            ]
            
            for x, y in search_positions:
                # 检查边界
                container_width = getattr(self.arranger, 'container_width', 1000)
                max_height = self.arranger.get_max_height() if hasattr(self.arranger, 'get_max_height') else 1000
                
                if x < 0 or y < 0 or x + width > container_width or y + height > max_height:
                    continue
                
                # 检查是否与其他图片重叠
                if not self._check_overlap(x, y, width, height, exclude_image=small_image):
                    # 计算得分（距离大图片越近越好）
                    distance = math.sqrt((x - large_x) ** 2 + (y - large_y) ** 2)
                    score = 1000 - distance  # 距离越近得分越高
                    
                    if score > best_score:
                        best_score = score
                        best_position = (x, y)
        
        return best_position
    
    def _calculate_position_score(self, x: int, y: int, width: int, height: int) -> float:
        """
        计算位置得分，优先靠近左上角和其他图片
        
        Args:
            x: X坐标
            y: Y坐标
            width: 宽度
            height: 高度
            
        Returns:
            float: 位置得分
        """
        # 基础得分：距离左上角越近得分越高
        distance_score = 1000 - math.sqrt(x**2 + y**2)
        
        # 紧密度得分：与其他图片越近得分越高
        proximity_score = 0
        for img in self.arranger.placed_images:
            img_x = img['x']
            img_y = img['y']
            img_w = img['width']
            img_h = img['height']
            
            # 计算到其他图片的最近距离
            dx = max(0, max(x - (img_x + img_w), img_x - (x + width)))
            dy = max(0, max(y - (img_y + img_h), img_y - (y + height)))
            distance = math.sqrt(dx**2 + dy**2)
            
            if distance < 50:  # 50像素内认为是邻近
                proximity_score += (50 - distance) * 2
        
        return distance_score + proximity_score
    
    def _check_overlap(self, x: int, y: int, width: int, height: int, 
                      exclude_image: Optional[Dict[str, Any]] = None) -> bool:
        """
        检查指定位置是否与现有图片重叠
        
        Args:
            x, y: 位置坐标
            width, height: 尺寸
            exclude_image: 排除的图片（用于移动图片时）
            
        Returns:
            bool: 是否重叠
        """
        for img in self.arranger.placed_images:
            if exclude_image and img is exclude_image:
                continue
            
            # 检查矩形重叠
            if not (x >= img['x'] + img['width'] or 
                   x + width <= img['x'] or
                   y >= img['y'] + img['height'] or
                   y + height <= img['y']):
                return True
        
        return False
    
    def _calculate_position_score(self, x: int, y: int, width: int, height: int) -> float:
        """
        计算位置得分，用于选择最优位置
        
        Args:
            x, y: 位置坐标
            width, height: 尺寸
            
        Returns:
            float: 位置得分
        """
        # 基础得分：越靠近左上角越好
        base_score = 10000 - (x + y) * 0.1
        
        # 紧凑性得分：与其他图片的接触面积
        contact_score = 0
        for img in self.arranger.placed_images:
            # 计算接触边长
            contact_length = self._calculate_contact_length(x, y, width, height, img)
            contact_score += contact_length * 0.5
        
        # 边界得分：靠近容器边界的奖励
        boundary_score = 0
        if x == 0:  # 左边界
            boundary_score += 50
        if y == 0:  # 上边界
            boundary_score += 50
        
        return base_score + contact_score + boundary_score
    
    def _calculate_contact_length(self, x: int, y: int, width: int, height: int, 
                                 other_img: Dict[str, Any]) -> float:
        """
        计算两个矩形的接触边长
        
        Args:
            x, y, width, height: 第一个矩形
            other_img: 第二个矩形信息
            
        Returns:
            float: 接触边长
        """
        ox, oy = other_img['x'], other_img['y']
        ow, oh = other_img['width'], other_img['height']
        
        contact_length = 0
        
        # 检查水平接触
        if y < oy + oh and y + height > oy:
            if x + width == ox:  # 右边接触
                contact_length += min(y + height, oy + oh) - max(y, oy)
            elif x == ox + ow:  # 左边接触
                contact_length += min(y + height, oy + oh) - max(y, oy)
        
        # 检查垂直接触
        if x < ox + ow and x + width > ox:
            if y + height == oy:  # 下边接触
                contact_length += min(x + width, ox + ow) - max(x, ox)
            elif y == oy + oh:  # 上边接触
                contact_length += min(x + width, ox + ow) - max(x, ox)
        
        return contact_length
    
    def intelligent_rotation_decision(self, image: Dict[str, Any], 
                                    available_space: Dict[str, int]) -> bool:
        """
        智能旋转决策：基于可用空间决定是否旋转图片
        
        Args:
            image: 图片信息
            available_space: 可用空间信息 {'width': w, 'height': h}
            
        Returns:
            bool: 是否应该旋转
        """
        width = image.get('width', 0)
        height = image.get('height', 0)
        
        space_width = available_space.get('width', 0)
        space_height = available_space.get('height', 0)
        
        # 如果原始方向能放下，检查旋转是否更优
        if width <= space_width and height <= space_height:
            # 计算原始方向的空间利用率
            original_utilization = (width * height) / (space_width * space_height)
            
            # 计算旋转后的空间利用率
            if height <= space_width and width <= space_height:
                rotated_utilization = (width * height) / (space_width * space_height)
                
                # 如果旋转后利用率更高，则旋转
                return rotated_utilization > original_utilization
            
            return False
        
        # 如果原始方向放不下，检查旋转后是否能放下
        if height <= space_width and width <= space_height:
            return True
        
        return False
    
    def calculate_layout_compactness(self) -> float:
        """
        计算布局紧凑度
        
        Returns:
            float: 紧凑度得分 (0-1)
        """
        if not self.arranger.placed_images:
            return 0.0
        
        # 计算边界框
        min_x = min(img['x'] for img in self.arranger.placed_images)
        min_y = min(img['y'] for img in self.arranger.placed_images)
        max_x = max(img['x'] + img['width'] for img in self.arranger.placed_images)
        max_y = max(img['y'] + img['height'] for img in self.arranger.placed_images)
        
        # 边界框面积
        bbox_area = (max_x - min_x) * (max_y - min_y)
        
        # 图片总面积
        total_image_area = sum(img['width'] * img['height'] for img in self.arranger.placed_images)
        
        # 紧凑度 = 图片面积 / 边界框面积
        return total_image_area / bbox_area if bbox_area > 0 else 0.0