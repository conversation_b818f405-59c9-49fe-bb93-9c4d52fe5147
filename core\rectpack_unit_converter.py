#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法统一单位转换模块
确保测试模式和生产模式使用一致的单位转换逻辑

模块化设计原则：
1. DRY原则：统一转换逻辑，避免重复
2. KISS原则：简单直接的转换方法
3. SOLID原则：单一职责，专注单位转换
4. YAGNI原则：只实现必要的转换功能
"""

import logging
from typing import Dict, Any, Tuple
from dataclasses import dataclass

log = logging.getLogger(__name__)

@dataclass
class ContainerConfig:
    """容器配置类"""
    base_width: int
    horizontal_expansion: int
    actual_width: int
    max_height: int
    spacing: int
    conversion_method: str

class RectPackUnitConverter:
    """RectPack算法统一单位转换器"""
    
    def __init__(self, ppi: int = 72):
        self.ppi = ppi
        self.cm_to_inch = 0.393701  # 1cm = 0.393701英寸
        log.info(f"RectPack单位转换器初始化: PPI={ppi}")
    
    def cm_to_px_test_mode(self, cm_value: float) -> int:
        """测试模式：cm直接转px（1:1比例）"""
        return int(round(cm_value))
    
    def cm_to_px_production_mode(self, cm_value: float) -> int:
        """生产模式：cm转px（使用PPI）"""
        inches = cm_value * self.cm_to_inch
        pixels = inches * self.ppi
        return int(round(pixels))
    
    def convert_image_size(self, width_cm: float, height_cm: float, is_test_mode: bool = False) -> Tuple[int, int]:
        """转换图片尺寸"""
        if is_test_mode:
            width_px = self.cm_to_px_test_mode(width_cm)
            height_px = self.cm_to_px_test_mode(height_cm)
            log.debug(f"测试模式图片尺寸转换: {width_cm}x{height_cm}cm -> {width_px}x{height_px}px")
        else:
            width_px = self.cm_to_px_production_mode(width_cm)
            height_px = self.cm_to_px_production_mode(height_cm)
            log.debug(f"生产模式图片尺寸转换: {width_cm}x{height_cm}cm -> {width_px}x{height_px}px (PPI={self.ppi})")
        
        return width_px, height_px
    
    def convert_container_config(self, 
                               canvas_width_cm: float,
                               horizontal_expansion_cm: float = 0,
                               max_height_cm: float = 5000,
                               image_spacing_cm: float = 0.1,
                               is_test_mode: bool = False) -> ContainerConfig:
        """转换容器配置"""
        
        if is_test_mode:
            # 测试模式：cm直接转px
            base_width = self.cm_to_px_test_mode(canvas_width_cm)
            horizontal_expansion = self.cm_to_px_test_mode(horizontal_expansion_cm)
            max_height = self.cm_to_px_test_mode(max_height_cm)
            spacing = self.cm_to_px_test_mode(image_spacing_cm)
            conversion_method = "测试模式: cm直接转px (1:1)"
        else:
            # 生产模式：cm转px使用PPI
            base_width = self.cm_to_px_production_mode(canvas_width_cm)
            horizontal_expansion = self.cm_to_px_production_mode(horizontal_expansion_cm)
            max_height = self.cm_to_px_production_mode(max_height_cm)
            spacing = self.cm_to_px_production_mode(image_spacing_cm)
            conversion_method = f"生产模式: cm转px (PPI={self.ppi})"
        
        actual_width = base_width + horizontal_expansion
        
        config = ContainerConfig(
            base_width=base_width,
            horizontal_expansion=horizontal_expansion,
            actual_width=actual_width,
            max_height=max_height,
            spacing=spacing,
            conversion_method=conversion_method
        )
        
        log.info(f"容器配置转换完成: {conversion_method}")
        log.info(f"  基础宽度: {base_width}px, 水平拓展: {horizontal_expansion}px")
        log.info(f"  实际宽度: {actual_width}px, 最大高度: {max_height}px, 间距: {spacing}px")
        
        return config
    
    def convert_position(self, x_cm: float, y_cm: float, is_test_mode: bool = False) -> Tuple[int, int]:
        """转换位置坐标"""
        if is_test_mode:
            x_px = self.cm_to_px_test_mode(x_cm)
            y_px = self.cm_to_px_test_mode(y_cm)
        else:
            x_px = self.cm_to_px_production_mode(x_cm)
            y_px = self.cm_to_px_production_mode(y_cm)
        
        return x_px, y_px


def create_unified_converter(ppi: int = 72) -> RectPackUnitConverter:
    """创建统一的单位转换器"""
    return RectPackUnitConverter(ppi)
