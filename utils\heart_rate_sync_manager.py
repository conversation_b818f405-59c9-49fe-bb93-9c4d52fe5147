#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Supabase心跳同步管理器
负责管理与Supabase的定期同步操作，遵循DRY、KISS、SOLID、YAGNI原则
"""

from datetime import datetime
from typing import Dict, Any, Optional, Callable, List, Tuple
from PyQt6.QtCore import QTimer, QObject, pyqtSignal

# 配置日志
from utils.log_config import get_logger
log = get_logger("HeartRateSyncManager")


class HeartRateSyncManager(QObject):
    """Supabase心跳同步管理器

    职责：
    1. 管理心跳同步定时器
    2. 执行定期同步操作
    3. 处理同步错误和恢复
    4. 提供同步状态监控
    """

    # 信号定义
    sync_started = pyqtSignal()
    sync_completed = pyqtSignal(bool)  # 参数：是否成功
    sync_error = pyqtSignal(str)  # 参数：错误信息
    sync_paused = pyqtSignal(str)  # 参数：暂停原因
    sync_resumed = pyqtSignal()

    def __init__(self, config_manager, supabase_helper, log_callback: Optional[Callable] = None):
        """初始化心跳同步管理器

        Args:
            config_manager: 配置管理器实例
            supabase_helper: Supabase辅助类实例
            log_callback: 日志回调函数，用于向UI显示日志
        """
        super().__init__()

        self.config_manager = config_manager
        self.supabase_helper = supabase_helper
        self.log_callback = log_callback

        # 同步状态管理
        self._timer: Optional[QTimer] = None
        self._is_enabled = False
        self._interval_seconds = 300  # 默认5分钟
        self._last_sync_time: Optional[datetime] = None
        self._sync_error_count = 0
        self._max_sync_errors = 3
        self._is_paused = False
        self._last_success_logged = False

        # 同步操作定义（按重要性排序）
        self._sync_operations: List[Tuple[str, Callable]] = []
        self._initialize_sync_operations()

        log.info("心跳同步管理器已初始化")

    def _initialize_sync_operations(self):
        """初始化同步操作列表"""
        self._sync_operations = [
            ("基础配置", self.config_manager.sync_from_supabase),
            ("心跳同步设置", self.config_manager.sync_heart_rate_settings),
            ("表格模式", self.config_manager.sync_table_mode),
            ("模糊查询", self.config_manager.sync_fuzzy_query),
            ("图库索引快速模式", self.config_manager.sync_db_scan_fast),
            ("测试模式", self.config_manager.sync_test_mode_settings),
        ]

    def _log(self, message: str, level: str = "info"):
        """记录日志并通知UI

        Args:
            message: 日志消息
            level: 日志级别 (info, warning, error)
        """
        # 记录到日志文件
        if level == "info":
            log.info(message)
        elif level == "warning":
            log.warning(message)
        elif level == "error":
            log.error(message)

        # 通知UI（如果有回调函数）
        if self.log_callback:
            try:
                self.log_callback(message)
            except Exception as e:
                log.error(f"日志回调失败: {str(e)}")

    def initialize(self) -> bool:
        """初始化心跳同步

        Returns:
            bool: 是否初始化成功
        """
        try:
            # 获取心跳同步设置
            heart_rate_settings = self.config_manager.get_heart_rate_settings()
            is_heart_rate = heart_rate_settings.get('is_heart_rate', False)
            heart_rate_time = heart_rate_settings.get('heart_rate_time', 300)

            # 更新内部状态
            self._is_enabled = is_heart_rate
            self._interval_seconds = self._validate_interval(heart_rate_time)

            # 重置状态
            self._sync_error_count = 0
            self._is_paused = False
            self._last_success_logged = False

            if self._is_enabled:
                # 启用心跳同步：启动定时器进行定期同步
                self._start_timer()
                self._log(f"✅ Supabase心跳同步已启动，间隔时间: {self._interval_seconds}秒")

                # 延迟执行首次同步，避免启动时的资源竞争
                QTimer.singleShot(2000, self._perform_sync)
                return True
            else:
                # 禁用心跳同步：只执行一次同步，不启动定时器
                self._stop_timer()
                self._log("ℹ️ Supabase心跳同步已禁用，执行一次性参数同步")

                # 延迟执行一次性同步，避免启动时的资源竞争
                QTimer.singleShot(2000, self._perform_one_time_sync)
                return True

        except Exception as e:
            self._log(f"❌ 初始化Supabase心跳同步失败: {str(e)}", "error")
            return False

    def _validate_interval(self, interval: int) -> int:
        """验证并调整心跳间隔时间

        Args:
            interval: 原始间隔时间（秒）

        Returns:
            int: 调整后的间隔时间
        """
        if interval < 30:
            self._log("⚠️ 心跳间隔时间过短，已调整为30秒", "warning")
            return 30
        elif interval > 86400:  # 24小时
            self._log("⚠️ 心跳间隔时间过长，已调整为24小时", "warning")
            return 86400
        return interval

    def _start_timer(self):
        """启动心跳定时器"""
        try:
            # 停止现有定时器
            self._stop_timer()

            # 创建新定时器
            self._timer = QTimer(self)
            self._timer.timeout.connect(self._perform_sync)
            self._timer.setSingleShot(False)  # 重复执行

            # 启动定时器
            interval_ms = self._interval_seconds * 1000
            self._timer.start(interval_ms)

            log.info(f"心跳定时器已启动，间隔: {self._interval_seconds}秒")

        except Exception as e:
            log.error(f"启动心跳定时器失败: {str(e)}")
            raise

    def _stop_timer(self):
        """停止心跳定时器"""
        try:
            if self._timer:
                self._timer.stop()
                self._timer.deleteLater()
                self._timer = None
                log.info("心跳定时器已停止")
        except Exception as e:
            log.error(f"停止心跳定时器失败: {str(e)}")

    def _perform_sync(self):
        """执行心跳同步（定期同步）"""
        try:
            # 首先检查是否启用了心跳同步
            if not self._is_enabled:
                log.debug("心跳同步已禁用，跳过同步")
                return

            # 检查是否被暂停
            if self._is_paused:
                return

            # 检查连续错误次数
            if self._sync_error_count >= self._max_sync_errors:
                self._pause_sync("连续错误次数过多")
                return

            # 检查前置条件
            if not self._check_sync_prerequisites():
                self._sync_error_count += 1
                return

            # 发送同步开始信号
            self.sync_started.emit()

            # 记录同步开始时间
            sync_start_time = datetime.now()
            log.info("开始执行Supabase心跳同步...")

            # 执行同步操作
            sync_success = self._execute_sync_operations()

            # 更新同步状态
            if sync_success:
                self._last_sync_time = sync_start_time
                self._sync_error_count = 0  # 重置错误计数

                # 仅在首次成功或错误恢复后显示日志
                if not self._last_success_logged:
                    self._log("✅ Supabase心跳同步成功")
                    self._last_success_logged = True

                self.sync_completed.emit(True)
            else:
                self._sync_error_count += 1
                self._last_success_logged = False
                self._log(f"⚠️ 心跳同步失败，连续错误次数: {self._sync_error_count}", "warning")
                self.sync_completed.emit(False)

        except Exception as e:
            self._sync_error_count += 1
            self._last_success_logged = False
            error_msg = f"执行Supabase心跳同步失败: {str(e)}"
            self._log(f"❌ {error_msg}", "error")
            self.sync_error.emit(error_msg)
            self.sync_completed.emit(False)

    def _perform_one_time_sync(self):
        """执行一次性同步（仅在禁用心跳同步时使用）"""
        try:
            # 检查前置条件
            if not self._check_sync_prerequisites():
                self._log("⚠️ 一次性同步前置条件不满足，跳过同步", "warning")
                return

            # 发送同步开始信号
            self.sync_started.emit()

            # 记录同步开始时间
            sync_start_time = datetime.now()
            log.info("开始执行一次性Supabase参数同步...")

            # 执行同步操作
            sync_success = self._execute_sync_operations()

            # 更新同步状态
            if sync_success:
                self._last_sync_time = sync_start_time
                self._log("✅ 一次性Supabase参数同步成功")
                self.sync_completed.emit(True)
            else:
                self._log("⚠️ 一次性Supabase参数同步失败", "warning")
                self.sync_completed.emit(False)

        except Exception as e:
            error_msg = f"执行一次性Supabase参数同步失败: {str(e)}"
            self._log(f"❌ {error_msg}", "error")
            self.sync_error.emit(error_msg)
            self.sync_completed.emit(False)

    def _check_sync_prerequisites(self) -> bool:
        """检查同步前置条件

        Returns:
            bool: 是否满足同步条件
        """
        # 检查Supabase连接状态
        if not self.supabase_helper.is_connected():
            log.warning("Supabase未连接，跳过心跳同步")
            return False

        # 检查用户认证状态
        if not self.supabase_helper.is_authenticated():
            log.warning("用户未认证，跳过心跳同步")
            return False

        return True

    def _execute_sync_operations(self) -> bool:
        """执行具体的同步操作

        Returns:
            bool: 是否所有关键同步操作都成功
        """
        try:
            sync_results = []
            heart_rate_updated = False

            # 执行同步操作
            for name, operation in self._sync_operations:
                try:
                    success = operation()
                    sync_results.append((name, success))

                    # 特殊处理心跳设置更新
                    if name == "心跳同步设置" and success:
                        heart_rate_updated = True

                except Exception as e:
                    log.error(f"同步{name}失败: {str(e)}")
                    sync_results.append((name, False))

            # 如果心跳设置发生变化，通知更新
            if heart_rate_updated:
                self._handle_heart_rate_settings_update()

            # 执行非关键同步操作
            self._execute_non_critical_sync()

            # 评估同步结果
            return self._evaluate_sync_results(sync_results)

        except Exception as e:
            log.error(f"执行同步操作失败: {str(e)}")
            return False

    def _handle_heart_rate_settings_update(self):
        """处理心跳设置更新"""
        try:
            # 获取最新设置
            heart_rate_settings = self.config_manager.get_heart_rate_settings()
            new_is_enabled = heart_rate_settings.get('is_heart_rate', False)
            new_interval = heart_rate_settings.get('heart_rate_time', 300)
            new_interval = self._validate_interval(new_interval)

            # 检查是否需要更新
            settings_changed = (
                self._is_enabled != new_is_enabled or
                self._interval_seconds != new_interval
            )

            if settings_changed:
                old_is_enabled = self._is_enabled
                self._is_enabled = new_is_enabled
                self._interval_seconds = new_interval

                if self._is_enabled:
                    # 启用心跳同步：启动定时器
                    self._start_timer()
                    self._log(f"🔄 心跳同步间隔已更新为: {self._interval_seconds}秒")
                else:
                    # 禁用心跳同步：停止定时器
                    self._stop_timer()
                    self._log("ℹ️ 心跳同步已禁用")

                    # 如果从启用变为禁用，不需要执行一次性同步
                    # 因为这是在运行时的设置更新，不是程序启动
                    if old_is_enabled:
                        self._log("ℹ️ 运行时禁用心跳同步，不执行一次性同步")

        except Exception as e:
            log.error(f"处理心跳设置更新失败: {str(e)}")

    def _execute_non_critical_sync(self):
        """执行非关键同步操作"""
        try:
            # 注意：为了避免额外的网络请求，这里不再执行密码刷新
            # 密码刷新将由主应用程序根据需要独立处理
            pass
        except Exception as e:
            log.error(f"执行非关键同步操作失败: {str(e)}")

    def _evaluate_sync_results(self, sync_results: List[Tuple[str, bool]]) -> bool:
        """评估同步结果

        Args:
            sync_results: 同步结果列表

        Returns:
            bool: 是否所有关键操作都成功
        """
        successful_syncs = sum(1 for _, success in sync_results if success)
        total_syncs = len(sync_results)

        if successful_syncs < total_syncs:
            failed_items = [name for name, success in sync_results if not success]
            # 定义关键操作
            critical_operations = {"基础配置", "心跳同步设置"}
            critical_failures = [name for name in failed_items if name in critical_operations]

            if critical_failures:
                log.warning(f"关键同步失败: {', '.join(critical_failures)}")
                return False

        return True

    def _pause_sync(self, reason: str):
        """暂停心跳同步

        Args:
            reason: 暂停原因
        """
        try:
            self._is_paused = True
            if self._timer:
                self._timer.stop()

            message = f"心跳同步已暂停: {reason}，将在10分钟后尝试恢复"
            self._log(f"⚠️ {message}", "warning")
            self.sync_paused.emit(reason)

            # 10分钟后尝试恢复
            QTimer.singleShot(600000, self._attempt_recovery)  # 10分钟 = 600000毫秒

        except Exception as e:
            log.error(f"暂停心跳同步失败: {str(e)}")

    def _attempt_recovery(self):
        """尝试恢复心跳同步"""
        try:
            # 重置状态
            self._sync_error_count = 0
            self._is_paused = False

            # 重新初始化
            if self.initialize():
                self._log("🔄 心跳同步已恢复")
                self.sync_resumed.emit()
            else:
                # 如果恢复失败，30分钟后再次尝试
                self._log("❌ 心跳同步恢复失败，将在30分钟后重试", "warning")
                QTimer.singleShot(1800000, self._attempt_recovery)  # 30分钟 = 1800000毫秒

        except Exception as e:
            log.error(f"恢复心跳同步失败: {str(e)}")
            # 如果恢复失败，30分钟后再次尝试
            QTimer.singleShot(1800000, self._attempt_recovery)

    def update_settings(self) -> bool:
        """更新心跳同步设置

        Returns:
            bool: 是否更新成功
        """
        try:
            return self.initialize()
        except Exception as e:
            log.error(f"更新心跳同步设置失败: {str(e)}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """获取心跳同步状态

        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            'is_enabled': self._is_enabled,
            'interval_seconds': self._interval_seconds,
            'is_paused': self._is_paused,
            'last_sync_time': self._last_sync_time,
            'sync_error_count': self._sync_error_count,
            'max_sync_errors': self._max_sync_errors,
            'timer_active': self._timer.isActive() if self._timer else False
        }

    def stop(self):
        """停止心跳同步管理器"""
        try:
            self._stop_timer()
            self._is_enabled = False
            self._is_paused = False
            log.info("心跳同步管理器已停止")
        except Exception as e:
            log.error(f"停止心跳同步管理器失败: {str(e)}")

    def force_sync(self) -> bool:
        """强制执行一次同步

        Returns:
            bool: 是否同步成功
        """
        try:
            if not self._check_sync_prerequisites():
                return False

            self._log("🔄 执行强制同步...")
            return self._execute_sync_operations()

        except Exception as e:
            self._log(f"❌ 强制同步失败: {str(e)}", "error")
            return False
